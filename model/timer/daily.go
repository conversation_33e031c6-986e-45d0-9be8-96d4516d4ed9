package timer

import (
	"fmt"
	"sync"
	"time"

	"sla-service/library/ent"
	"sla-service/library/ent/event"
	"sla-service/library/ent/sla"
	"sla-service/library/logger"
	"sla-service/library/mysql"
)

const (
	DenominatorSourceEvent    = "event"
	DenominatorSourceMinutes  = "time_minutes"
	DenominatorSource5Minutes = "time_5minutes"

	ComparisonGT  = "gt"
	ComparisonGTE = "gte"
	ComparisonLT  = "lt"
	ComparisonLTE = "lte"

	PeriodMonthly   = "monthly"
	PeriodQuarterly = "quarterly"
	PeriodYearly    = "yearly"
)

// SLI 单个SLI信息
type SLI struct {
	ID                int64
	Name              string
	DenominatorSource string  // 分母数据来源：event/time_minutes/time_5minutes
	Comparison        string  // 比较方式：gt/gte/lt/lte
	Threshold         float64 // 阈值
	DecimalPlaces     int     // 数据展示时保留的小数位数
}

// 单个服务信息
type Service struct {
	ID   int64
	Name string
}

type SLA struct {
	ID        int64
	Name      string
	StartTime time.Time
	EndTime   time.Time
	Services  []*Service // 所属服务列表
	SLIs      []*SLI     // 所属SLI列表
}

// Product 产品信息
type Product struct {
	ID   int64  // 产品ID
	Name string // 产品名称
	SLAs []*SLA // 所属SLA列表
}

//
//
//

// 根据SLA周期类型计算各周期的开始和结束时间
func getStartAndEndTime(period string) (time.Time, time.Time) {
	now := time.Now()
	// 设置当天23:59:59为结束时间
	endTimeOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())

	var startTime time.Time
	switch period {
	case "monthly": // 月度周期
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	case "quarterly": // 季度周期
		quarter := (int(now.Month()) - 1) / 3
		firstMonthOfQuarter := time.Month(quarter*3 + 1)
		startTime = time.Date(now.Year(), firstMonthOfQuarter, 1, 0, 0, 0, 0, now.Location())
	case "yearly": // 年度周期
		startTime = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	default: // 默认使用月度周期
		logger.Error("[calPeriodStartAndEndTime] 未知的SLA周期类型: %s", period)
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	}

	return startTime, endTimeOfDay
}

// 获取所有产品及其服务和SLI信息
func getProductList() ([]*Product, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("获取数据库客户端失败: %v", err)
		return nil, err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	slas, err := db.SLA.Query().
		Where(sla.StatusEQ("normal")).
		WithSlis().WithServices().
		All(ctx)
	cancel()
	if err != nil {
		logger.Error("获取SLA列表失败: %v", err)
		return nil, err
	}

	// 产品数组转Map
	productMap := make(map[int64]*Product)
	for _, sla := range slas {
		product, ok := productMap[sla.ProductID]
		if !ok {
			product = &Product{ID: sla.ProductID, Name: sla.ProductName, SLAs: []*SLA{}}
			productMap[sla.ProductID] = product
		}

		//
		startTime, endTime := getStartAndEndTime(sla.Period)
		slaObj := &SLA{ID: sla.ID, Name: sla.Name, StartTime: startTime, EndTime: endTime}

		// 为每个SLA创建SLI并添加到对应的SLA中
		for _, sli := range sla.Edges.Slis {
			slaObj.SLIs = append(slaObj.SLIs, &SLI{
				ID:                sli.ID,
				Name:              sli.Name,
				DenominatorSource: sli.DenominatorSource,
				Comparison:        sli.Comparison,
				Threshold:         sli.Threshold,
				DecimalPlaces:     sli.DecimalPlaces,
			})
		}

		// 为每个SLA创建Service并添加到对应的产品中
		for _, svc := range sla.Edges.Services {
			slaObj.Services = append(slaObj.Services, &Service{ID: svc.ID, Name: svc.Name})
		}

		product.SLAs = append(product.SLAs, slaObj)
	}

	// 将productMap转换为[]*Product
	result := make([]*Product, 0, len(productMap))
	for _, product := range productMap {
		result = append(result, product)
	}

	return result, nil
}

// 获取SLA下所有服务在周期内的所有event
func getEventList(slaId int64, startTime time.Time, endTime time.Time) ([]*ent.Event, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("获取数据库客户端失败: %v", err)
		return nil, err
	}

	// 一次性查询该服务在周期内的所有event数据
	ctx, cancel := mysql.ContextWithTimeout()
	events, err := db.Event.Query().
		Where(
			event.SLAID(slaId),
			event.HappenedAtGTE(startTime),
			event.HappenedAtLTE(endTime),
		).
		All(ctx)
	cancel()
	if err != nil && !ent.IsNotFound(err) {
		logger.Error("查询服务事件数据失败: %v", err)
		return nil, err
	}

	return events, nil
}

type CalRet struct {
	Count       int     // 参与计算的事件数量
	Numerator   float64 // 分子
	Denominator float64 // 分母
}

// 使用预先查询的events数据计算指定服务的指定SLI在指定周期内的SLA
func calculate(sla *SLA) ([]*ent.CalculationCreate, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("获取数据库客户端失败: %v", err)
		return nil, err
	}

	// 查询SLA下的所有服务在周期内的所有event数据
	events, err := getEventList(sla.ID, sla.StartTime, sla.EndTime)
	if err != nil {
		return nil, err
	}

	// 按service-sli分组，分别计算分子、分母值
	eventMap := make(map[string]*CalRet)
	for _, evt := range events {
		key := fmt.Sprintf("%d-%d", evt.ServiceID, evt.SliID)
		ret, ok := eventMap[key]
		if !ok {
			ret = &CalRet{Count: 0, Numerator: 0, Denominator: 0}
			eventMap[key] = ret // 添加这行
		}

		ret.Numerator += evt.Numerator
		ret.Denominator += evt.Denominator
		ret.Count += 1
	}

	// 遍历服务下的每个SLI，使用分组后的event数据计算SLA
	expectedSize := len(sla.Services) * len(sla.SLIs)
	calculations := make([]*ent.CalculationCreate, 0, expectedSize)
	for _, service := range sla.Services {
		for _, sli := range sla.SLIs {
			var count int
			var numerator, denominator float64
			key := fmt.Sprintf("%d-%d", service.ID, sli.ID)
			if ret, ok := eventMap[key]; ok {
				count = ret.Count
				numerator = ret.Numerator
				denominator = ret.Denominator
			}

			// 时间类型的分母转换、覆盖
			switch sli.DenominatorSource {
			case "event":
			case "time_minutes":
				denominator = sla.EndTime.Sub(sla.StartTime).Seconds()
			case "time_5minutes":
				denominator = sla.EndTime.Sub(sla.StartTime).Seconds() / 300
			default:
				return nil, fmt.Errorf("未知的分母数据来源: %s", sli.DenominatorSource)
			}

			// 计算SLA值（百分比）
			var slaValue float64
			if denominator > 0 {
				slaValue = (1 - numerator/denominator) * 100
			}

			// 判断是否违反阈值
			breached := false
			switch sli.Comparison {
			case "gt":
				breached = slaValue > sli.Threshold
			case "gte":
				breached = slaValue >= sli.Threshold
			case "lt":
				breached = slaValue < sli.Threshold
			case "lte":
				breached = slaValue <= sli.Threshold
			}

			// 创建 calculation 记录
			calculations = append(calculations, db.Calculation.Create().
				SetServiceID(service.ID).
				SetServiceName(service.Name).
				SetSliID(sli.ID).
				SetSliName(sli.Name).
				SetPeriodStart(sla.StartTime).
				SetPeriodEnd(sla.EndTime).
				SetNumerator(numerator).
				SetDenominator(denominator).
				SetValue(slaValue).
				SetBreached(breached).
				SetEventCount(count),
			)
		}
	}

	return calculations, nil
}

// batchCreateCalculations 批量创建calculation记录
func batchCreateCalculations(calculations []*ent.CalculationCreate) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("获取数据库客户端失败: %v", err)
		return err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	defer cancel()

	// 使用事务批量创建
	tx, err := db.Tx(ctx)
	if err != nil {
		return fmt.Errorf("创建事务失败: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// 批量创建calculation记录
	for _, calc := range calculations {
		_, err = calc.Save(ctx)
		if err != nil {
			return fmt.Errorf("创建calculation记录失败: %w", err)
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

//
//
//

// 定时任务：计算每日SLA
func JobCalculateDailySLA() {
	logger.Info("[JobCalculateDailySLA] 开始执行每日SLA计算任务")

	// step1. 获取产品列表
	products, err := getProductList()
	if err != nil {
		logger.Error("[JobCalculateDailySLA] 获取产品列表失败: %v", err)
		return
	}
	logger.Info("[JobCalculateDailySLA] 获取到 %d 个产品", len(products))

	// 使用worker pool控制并发数
	const maxWorkers = 5
	semaphore := make(chan struct{}, maxWorkers)
	var wg sync.WaitGroup

	for _, product := range products {
		wg.Add(1)
		go func(p *Product) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 处理产品的SLA计算
			processProduct(p)
		}(product)
	}

	wg.Wait()

	logger.Info("[JobCalculateDailySLA] 每日SLA计算任务执行完成")
}

func processProduct(product *Product) {
	logger.Info("[JobCalculateDailySLA] 开始计算产品 %s 的SLA", product.Name)

	// 遍历产品下的每个服务
	for _, sla := range product.SLAs {
		// step4. 使用预先查询的events数据计算指定服务的指定SLI在指定周期内的SLA
		calculations, err := calculate(sla)
		if err != nil {
			logger.Error("[JobCalculateDailySLA] 计算SLA失败: %v", err)
		}

		// step4. 批量创建calculation记录
		if len(calculations) > 0 {
			err = batchCreateCalculations(calculations)
			if err != nil {
				logger.Error("[JobCalculateDailySLA] 批量创建calculation记录失败: %v", err)
				return
			}
			logger.Info("[JobCalculateDailySLA] 成功创建 %d 条calculation记录", len(calculations))
		}
	}
}
