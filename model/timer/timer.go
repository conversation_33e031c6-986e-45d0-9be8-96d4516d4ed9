package timer

import (
	"github.com/robfig/cron/v3"
)

var c *cron.Cron

// Start 启动定时巡检任务
func Start(conf *Config) error {
	l := &CronLogger{}
	c = cron.New(cron.WithLogger(l), cron.WithSeconds(), cron.With<PERSON><PERSON><PERSON>(guard, cron.SkipIfStillRunning(l)))
	defer c.Start()

	// =====================================
	// 		       服务节点探测
	// =====================================
	// _, err = c.AddFunc("@every 10s", JobUpdateInstanceNo)
	// if err != nil {
	// 	return fmt.Errorf("failed to add timer JobUpdateInstanceNo, error=(%v)", err)
	// }

	// =====================================
	// 			   已用内存同步
	// =====================================
	// spec := fmt.Sprintf("@every %ds", conf.MemorySync.Interval)
	// _, err = c.AddFunc(spec, JobMemorySync)
	// if err != nil {
	// 	return fmt.Errorf("failed to add timer JobMemorySync, error=(%v)", err)
	// }

	// =====================================
	// 			   SLA计算任务
	// =====================================
	// _, err := c.AddFunc("0 1 * * *", JobCalculateDailySLA) // 每天凌晨1点执行
	// if err != nil {
	// 	return fmt.Errorf("failed to add timer JobCalculateDailySLA, error=(%v)", err)
	// }

	return nil
}

// Stop 停止定时巡检任务
func Stop() {
	if c != nil {
		ctx := c.Stop()
		<-ctx.Done()
	}
}
