package timer

import (
	"fmt"

	"github.com/robfig/cron/v3"
)

var c *cron.Cron

// Start 启动定时巡检任务
func Start(conf *Config) error {
	l := &CronLogger{}
	c = cron.New(cron.WithLogger(l), cron.WithSeconds(), cron.With<PERSON><PERSON><PERSON>(guard, cron.SkipIfStillRunning(l)))
	defer c.Start()

	// =====================================
	// 		       服务节点探测
	// =====================================
	_, err := c.AddFunc("@hourly", JobCalculateDailySLA) // 每小时执行1次
	if err != nil {
		return fmt.Errorf("failed to add timer JobUpdateInstanceNo, error=(%v)", err)
	}

	return nil
}

// Stop 停止定时巡检任务
func Stop() {
	if c != nil {
		ctx := c.Stop()
		<-ctx.Done()
	}
}
