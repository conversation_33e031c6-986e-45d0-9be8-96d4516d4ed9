package timer

import (
	"fmt"
	"runtime"

	"github.com/robfig/cron/v3"

	"sla-service/library/logger"
)

type CronLogger struct{}

// Info logs routine messages about cron's operation. do nothing
func (l *CronLogger) Info(msg string, keysAndValues ...any) {}

// Error logs an error condition.
func (l *CronLogger) Error(err error, msg string, keysAndValues ...any) {
	logger.Error("[Cron] error=(%v), %s, %+v", err, msg, keysAndValues)
}

// 对于所有job的panic，统一进行错误处理，发到钉钉报警
func guard(job cron.Job) cron.Job {
	return cron.FuncJob(func() {
		defer func() {
			if r := recover(); r != nil {
				const size = 64 << 10
				buf := make([]byte, size)
				buf = buf[:runtime.Stack(buf, false)]
				err, ok := r.(error)
				if !ok {
					err = fmt.Errorf("%v", r)
				}
				logger.Error("[Timer Panic Occured] %v", err)
				logger.Error("[Recovery From Panic] %v", string(buf))
				// dbot.Ding(dbot.RobotUSKidTech, "定时任务报错")
			}
		}()
		job.Run()
	})
}

// =====================================
//   			Configs
// =====================================

type Config struct {
}
