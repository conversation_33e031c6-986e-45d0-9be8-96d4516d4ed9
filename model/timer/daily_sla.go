package timer

import (
	"sync"
	"time"

	"sla-service/library/logger"
	"sla-service/model/calculation"
)

// 定时任务：计算每日SLA
func JobCalculateDailySLA() {
	logger.Info("[JobCalculateDailySLA] 开始执行每日SLA计算任务")

	// step1. 获取产品列表
	products, err := calculation.GetProductList(time.Now())
	if err != nil {
		logger.Error("[JobCalculateDailySLA] 获取产品列表失败: %v", err)
		return
	}
	logger.Info("[JobCalculateDailySLA] 获取到 %d 个产品", len(products))

	// 使用worker pool控制并发数
	const maxWorkers = 5
	semaphore := make(chan struct{}, maxWorkers)
	wg := &sync.WaitGroup{}

	for _, product := range products {
		wg.Add(1)
		go func(p *calculation.Product) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 处理产品的SLA计算
			logger.Info("[JobCalculateDailySLA] 开始计算产品 %s 的SLA", p.Name)
			// 遍历产品下的每个服务
			for _, sla := range p.SLAs {
				// step4. 使用预先查询的events数据计算指定服务的指定SLI在指定周期内的SLA
				err := calculation.Daily(sla)
				if err != nil {
					logger.Error("[JobCalculateDailySLA] 计算SLA失败: %v", err)
					continue
				}
				logger.Info("[JobCalculateDailySLA] 产品 %s 的SLA计算完成", p.Name)
			}
		}(product)
	}
	wg.Wait()

	logger.Info("[JobCalculateDailySLA] 每日SLA计算任务执行完成")
}
