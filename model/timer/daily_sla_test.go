package timer

import (
	"context"
	"testing"

	"sla-service/env"
	"sla-service/library/mysql"
)

func TestJobCalculateDailySLA(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct{}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T)
	}{
		{
			name: "正常执行",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.Product.Create().SetName("noah").SetAlias("Noah").SetOwner("yuanzaiping_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())

				db.SLA.Create().SetProductID(1).SetProductName("Redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(2).SetProductName("Noah").SetName("系统可用性").SetPeriod("quarterly").SetStatus("normal").SaveX(context.Background())

				db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("time_minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(2).SetSLAName("系统可用性").SetName("服务可用性").SetUnit("%").SetNumeratorName("pv_lost").SetDenominatorName("pv_total").SetDenominatorSource("event").SetComparison("gte").SetThreshold(99.8).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())

				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("loan_risk_plms").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(2).SetProductName("Noah").SetSLAID(2).SetSLAName("系统可用性").SetName("监控系统").SetStatus("normal").SaveX(context.Background())
			},
			expect: func(t *testing.T) {
				db, _ := mysql.Database()
				cals, err := db.Calculation.Query().All(context.Background())
				if err != nil {
					t.Errorf("Failed to query calculations: %v", err)
				}
				if len(cals) != 3 {
					t.Errorf("Expected 3 calculations, got %d", len(cals))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			JobCalculateDailySLA()
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
