package timer

import (
	"context"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"sla-service/env"
	"sla-service/library/ent"
	"sla-service/library/mysql"
)

func Test_getStartAndEndTime(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		period string
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, time.Time, time.Time)
	}{
		{
			name: "月度周期测试",
			args: args{
				period: PeriodMonthly,
			},
			expect: func(t *testing.T, start time.Time, end time.Time) {
				now := time.Now()
				if !start.Equal(time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())) {
					t.<PERSON><PERSON>rf("Start time should be the first day of the month, got %v", start)
				}
				if !end.Equal(time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())) {
					t.<PERSON>rrorf("End time should be the current day at 23:59:59, got %v", end)
				}
			},
		},
		{
			name: "季度周期测试",
			args: args{
				period: PeriodQuarterly,
			},
			expect: func(t *testing.T, start time.Time, end time.Time) {
				now := time.Now()
				quarter := (int(now.Month()) - 1) / 3
				firstMonthOfQuarter := time.Month(quarter*3 + 1)
				if !start.Equal(time.Date(now.Year(), firstMonthOfQuarter, 1, 0, 0, 0, 0, now.Location())) {
					t.Errorf("Start time should be the first day of the quarter, got %v", start)
				}
				if !end.Equal(time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())) {
					t.Errorf("End time should be the current day at 23:59:59, got %v", end)
				}
			},
		},
		{
			name: "年度周期测试",
			args: args{
				period: PeriodYearly,
			},
			expect: func(t *testing.T, start time.Time, end time.Time) {
				now := time.Now()
				if !start.Equal(time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())) {
					t.Errorf("Start time should be the first day of the year, got %v", start)
				}
				if !end.Equal(time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())) {
					t.Errorf("End time should be the current day at 23:59:59, got %v", end)
				}
			},
		},
		{
			name: "未知周期测试（默认月度）",
			args: args{
				period: "unknown",
			},
			expect: func(t *testing.T, start time.Time, end time.Time) {
				now := time.Now()
				if !start.Equal(time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())) {
					t.Errorf("Start time should be the first day of the month, got %v", start)
				}
				if !end.Equal(time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())) {
					t.Errorf("End time should be the current day at 23:59:59, got %v", end)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotStart, gotEnd := getStartAndEndTime(tt.args.period)
			if tt.expect != nil {
				tt.expect(t, gotStart, gotEnd)
			}
		})
	}
}

func Test_getProductList(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	tests := []struct {
		name   string
		before func()
		expect func(*testing.T, []*Product, error)
	}{
		{
			name: "正常获取产品列表",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.Product.Create().SetName("noah").SetAlias("Noah").SetOwner("yuanzaiping_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())

				db.SLA.Create().SetProductID(1).SetProductName("Redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(2).SetProductName("Noah").SetName("系统可用性").SetPeriod("quarterly").SetStatus("normal").SaveX(context.Background())

				db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(2).SetSLAName("系统可用性").SetName("服务可用性").SetUnit("%").SetNumeratorName("pv_lost").SetDenominatorName("pv_total").SetDenominatorSource("event").SetComparison("gte").SetThreshold(99.8).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())

				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("loan_risk_plms").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(2).SetProductName("Noah").SetSLAID(2).SetSLAName("系统可用性").SetName("监控系统").SetStatus("normal").SaveX(context.Background())
			},
			expect: func(t *testing.T, products []*Product, err error) {
				if err != nil {
					t.Errorf("getProductList() error = %v", err)
					return
				}

				// 验证返回的产品列表结构
				if products == nil {
					t.Error("getProductList() returned nil")
					return
				}

				// 验证产品列表的基本结构
				t.Logf("获取到 %d 个产品", len(products))

				// 应该有2个产品：Redis和Noah
				if len(products) != 2 {
					t.Errorf("期望获取到2个产品，实际获取到%d个", len(products))
					return
				}

				// 验证产品信息
				productMap := make(map[string]*Product)
				for _, product := range products {
					if product.ID == 0 {
						t.Error("Product ID should not be zero")
					}
					if product.Name == "" {
						t.Error("Product Name should not be empty")
					}
					if product.SLAs == nil {
						t.Error("Product SLAs should not be nil")
					}
					productMap[product.Name] = product
					t.Logf("产品: %s (ID: %d), SLA数量: %d", product.Name, product.ID, len(product.SLAs))
				}

				// 验证Redis产品
				redisProduct, exists := productMap["Redis"]
				if !exists {
					t.Error("应该包含Redis产品")
					t.Logf("实际的产品名称: %v", func() []string {
						var names []string
						for name := range productMap {
							names = append(names, name)
						}
						return names
					}())
				} else {
					if len(redisProduct.SLAs) != 1 {
						t.Errorf("Redis产品应该有1个SLA，实际有%d个", len(redisProduct.SLAs))
					} else {
						redisSLA := redisProduct.SLAs[0]
						if redisSLA.Name != "集群版SLA" {
							t.Errorf("Redis SLA名称应该是'集群版SLA'，实际是'%s'", redisSLA.Name)
						}
						if len(redisSLA.Services) != 2 {
							t.Errorf("Redis SLA应该有2个服务，实际有%d个", len(redisSLA.Services))
						} else {
							t.Logf("Redis SLA服务: %v", []string{redisSLA.Services[0].Name, redisSLA.Services[1].Name})
						}
						if len(redisSLA.SLIs) != 1 {
							t.Errorf("Redis SLA应该有1个SLI，实际有%d个", len(redisSLA.SLIs))
						} else {
							redisSLI := redisSLA.SLIs[0]
							if redisSLI.Name != "服务可用性" {
								t.Errorf("Redis SLI名称应该是'服务可用性'，实际是'%s'", redisSLI.Name)
							}
							if redisSLI.Threshold != 99.99 {
								t.Errorf("Redis SLI阈值应该是99.99，实际是%f", redisSLI.Threshold)
							}
							t.Logf("Redis SLI: %s, 阈值: %f, 小数位数: %d", redisSLI.Name, redisSLI.Threshold, redisSLI.DecimalPlaces)
						}
					}
				}

				// 验证Noah产品
				noahProduct, exists := productMap["Noah"]
				if !exists {
					t.Error("应该包含Noah产品")
					t.Logf("实际的产品名称: %v", func() []string {
						var names []string
						for name := range productMap {
							names = append(names, name)
						}
						return names
					}())
				} else {
					if len(noahProduct.SLAs) != 1 {
						t.Errorf("Noah产品应该有1个SLA，实际有%d个", len(noahProduct.SLAs))
					} else {
						noahSLA := noahProduct.SLAs[0]
						if noahSLA.Name != "系统可用性" {
							t.Errorf("Noah SLA名称应该是'系统可用性'，实际是'%s'", noahSLA.Name)
						}
						if len(noahSLA.Services) != 1 {
							t.Errorf("Noah SLA应该有1个服务，实际有%d个", len(noahSLA.Services))
						} else {
							t.Logf("Noah SLA服务: %s", noahSLA.Services[0].Name)
						}
						if len(noahSLA.SLIs) != 1 {
							t.Errorf("Noah SLA应该有1个SLI，实际有%d个", len(noahSLA.SLIs))
						} else {
							noahSLI := noahSLA.SLIs[0]
							if noahSLI.Name != "服务可用性" {
								t.Errorf("Noah SLI名称应该是'服务可用性'，实际是'%s'", noahSLI.Name)
							}
							if noahSLI.Threshold != 99.8 {
								t.Errorf("Noah SLI阈值应该是99.8，实际是%f", noahSLI.Threshold)
							}
							if noahSLI.DenominatorSource != "event" {
								t.Errorf("Noah SLI分母来源应该是'event'，实际是'%s'", noahSLI.DenominatorSource)
							}
							t.Logf("Noah SLI: %s, 阈值: %f, 分母来源: %s", noahSLI.Name, noahSLI.Threshold, noahSLI.DenominatorSource)
						}
					}
				}

			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			got, err := getProductList()
			if tt.expect != nil {
				tt.expect(t, got, err)
			}
		})
	}
}

func Test_getEventList(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		slaId     int64
		startTime time.Time
		endTime   time.Time
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, []*ent.Event, error)
	}{
		{
			name: "空记录",
			args: args{
				slaId:     1,
				startTime: time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC),
				endTime:   time.Date(2025, 7, 31, 23, 59, 59, 0, time.UTC),
			},
			expect: func(t *testing.T, events []*ent.Event, err error) {
				if err != nil {
					t.Errorf("getEventList() error = %v", err)
					return
				}
				// 验证返回的事件列表
				if events == nil {
					t.Error("getEventList() returned nil")
					return
				}
				if len(events) != 0 {
					t.Errorf("getEventList() returned %d events, want 0", len(events))
				}
			},
		},
		{
			name: "正常查询事件列表",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(1).SetProductName("Redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())

				// 准备事件数据
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetSliID(1).SetEventType("machine_down").SetNumerator(10).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 1, 0, 5, 24, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetSliID(1).SetEventType("machine_down").SetNumerator(20).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 5, 12, 16, 54, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetSliID(1).SetEventType("machine_down").SetNumerator(30).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 24, 18, 10, 30, 0, time.UTC)).SaveX(context.Background())
			},
			args: args{
				slaId:     1,
				startTime: time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC),
				endTime:   time.Date(2025, 7, 31, 23, 59, 59, 0, time.UTC),
			},
			expect: func(t *testing.T, events []*ent.Event, err error) {
				if err != nil {
					t.Errorf("getEventList() error = %v", err)
					return
				}
				// 验证返回的事件列表
				if events == nil {
					t.Error("getEventList() returned nil")
					return
				}

				if len(events) != 3 {
					t.Errorf("getEventList() returned %d events, want 3", len(events))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			got, err := getEventList(tt.args.slaId, tt.args.startTime, tt.args.endTime)
			if tt.expect != nil {
				tt.expect(t, got, err)
			}
		})
	}
}

func Test_calculate(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		sla *SLA
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, []*ent.CalculationCreate, error)
	}{
		{
			name: "时间类型分母SLA计算",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(1).SetProductName("Redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("loan_risk_plms").SetStatus("normal").SaveX(context.Background())

				// 准备事件数据 31.248 / 1874.88
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetSliID(1).SetEventType("machine_down").SetNumerator(244).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 1, 0, 5, 24, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetSliID(1).SetEventType("machine_down").SetNumerator(782).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 5, 12, 16, 54, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetSliID(1).SetEventType("machine_down").SetNumerator(849).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 24, 18, 10, 30, 0, time.UTC)).SaveX(context.Background())
			},
			args: args{
				sla: &SLA{
					ID:        1,
					Name:      "test-sla-time",
					StartTime: time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC),     // 月初
					EndTime:   time.Date(2025, 7, 23, 23, 59, 59, 0, time.UTC), // 当天
					Services: []*Service{
						{ID: 3, Name: "test-service-3"},
					},
					SLIs: []*SLI{
						{
							ID:                2,
							Name:              "test-sli-time",
							DenominatorSource: DenominatorSourceMinutes,
							Comparison:        ComparisonGT,
							Threshold:         99.5,
							DecimalPlaces:     3,
						},
					},
				},
			},
			expect: func(t *testing.T, calculations []*ent.CalculationCreate, err error) {
				if calculations == nil {
					t.Error("calculateSLA() returned nil")
					return
				}

				// 验证计算结果的数量：1个服务 × 1个SLI = 1个计算结果
				expectedCount := 1
				if len(calculations) != expectedCount {
					t.Errorf("calculateSLA() returned %d calculations, want %d", len(calculations), expectedCount)
				}
			},
		},
		// {
		// 	name: "事件类型分母SLA计算",
		// 	before: func() {
		// 		db, _ := mysql.Database()
		// 		db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
		// 		db.Product.Create().SetName("noah").SetAlias("Noah").SetOwner("yuanzaiping_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())

		// 		db.SLA.Create().SetProductID(1).SetProductName("Redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
		// 		db.SLA.Create().SetProductID(2).SetProductName("Noah").SetName("系统可用性").SetPeriod("quarterly").SetStatus("normal").SaveX(context.Background())

		// 		db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
		// 		db.SLI.Create().SetSLAID(2).SetSLAName("系统可用性").SetName("服务可用性").SetUnit("%").SetNumeratorName("pv_lost").SetDenominatorName("pv_total").SetDenominatorSource("event").SetComparison("gte").SetThreshold(99.8).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())

		// 		db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())
		// 		db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("loan_risk_plms").SetStatus("normal").SaveX(context.Background())
		// 		db.Service.Create().SetProductID(2).SetProductName("Noah").SetSLAID(2).SetSLAName("系统可用性").SetName("监控系统").SetStatus("normal").SaveX(context.Background())

		// 		// 准备事件数据
		// 		db.Event.Create().SetSLAID(2).SetServiceID(2).SetSliID(2).SetEventType("machine_down").SetNumerator(10).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 1, 0, 5, 24, 0, time.UTC)).SaveX(context.Background())
		// 		db.Event.Create().SetSLAID(2).SetServiceID(2).SetSliID(2).SetEventType("machine_down").SetNumerator(20).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 5, 12, 16, 54, 0, time.UTC)).SaveX(context.Background())
		// 		db.Event.Create().SetSLAID(2).SetServiceID(2).SetSliID(2).SetEventType("machine_down").SetNumerator(30).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 24, 18, 10, 30, 0, time.UTC)).SaveX(context.Background())
		// 	},
		// 	args: args{
		// 		sla: &SLA{
		// 			ID:        1,
		// 			Name:      "test-sla",
		// 			StartTime: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
		// 			EndTime:   time.Date(2024, 1, 1, 23, 59, 59, 0, time.UTC),
		// 			Services: []*Service{
		// 				{ID: 1, Name: "test-service-1"},
		// 				{ID: 2, Name: "test-service-2"},
		// 			},
		// 			SLIs: []*SLI{
		// 				{
		// 					ID:                1,
		// 					Name:              "test-sli-1",
		// 					DenominatorSource: DenominatorSourceEvent,
		// 					Comparison:        ComparisonLT,
		// 					Threshold:         99.9,
		// 					DecimalPlaces:     2,
		// 				},
		// 			},
		// 		},
		// 	},
		// 	expect: func(t *testing.T, calculations []*ent.CalculationCreate, err error) {
		// 		if calculations == nil {
		// 			t.Error("calculateSLA() returned nil")
		// 			return
		// 		}

		// 		// 验证计算结果的数量：2个服务 × 1个SLI = 2个计算结果
		// 		expectedCount := 2
		// 		if len(calculations) != expectedCount {
		// 			t.Errorf("calculateSLA() returned %d calculations, want %d", len(calculations), expectedCount)
		// 		}
		// 	},
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			got, err := calculate(tt.args.sla)
			if tt.expect != nil {
				tt.expect(t, got, err)
			}
		})
	}
}

func TestJobCalculateDailySLA(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			JobCalculateDailySLA()
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("JobUpdateInstanceNo() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// TestCalculatePeriod 函数已被 TestGetStartAndEndTime 替代，因为 calculatePeriod 函数已不存在

// TestCalculateTimeDenominator 函数已删除，因为 calculateTimeDenominator 函数已不存在

// TestCalculateSLAForServiceWithEvents 函数已删除，因为 calculateSLAForServiceWithEvents 函数已不存在

func TestBatchCreateCalculations(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	client, err := mysql.Database()
	if err != nil {
		t.Fatalf("Failed to get database client: %v", err)
	}

	type args struct {
		calculations []*ent.CalculationCreate
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "批量创建calculation记录测试",
			before: func() {
				// 可以在这里准备测试数据
			},
			args: args{
				calculations: []*ent.CalculationCreate{
					client.Calculation.Create().
						SetServiceID(1).
						SetServiceName("test-service-1").
						SetSliID(1).
						SetSliName("test-sli-1").
						SetPeriodStart(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)).
						SetPeriodEnd(time.Date(2024, 1, 1, 23, 59, 59, 0, time.UTC)).
						SetNumerator(10).
						SetDenominator(1000).
						SetValue(99.0).
						SetBreached(false).
						SetEventCount(2),
					client.Calculation.Create().
						SetServiceID(2).
						SetServiceName("test-service-2").
						SetSliID(2).
						SetSliName("test-sli-2").
						SetPeriodStart(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)).
						SetPeriodEnd(time.Date(2024, 1, 1, 23, 59, 59, 0, time.UTC)).
						SetNumerator(5).
						SetDenominator(500).
						SetValue(99.5).
						SetBreached(true).
						SetEventCount(1),
				},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				// 可以在这里验证数据是否正确创建
				// 例如查询数据库验证记录是否存在
			},
		},
		{
			name: "空列表测试",
			args: args{
				calculations: []*ent.CalculationCreate{},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				// 空列表应该正常处理，不报错
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := batchCreateCalculations(tt.args.calculations)
			if (err != nil) != tt.wantErr {
				t.Errorf("batchCreateCalculations() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
