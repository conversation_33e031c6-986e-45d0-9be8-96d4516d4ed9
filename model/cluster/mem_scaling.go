package cluster

import (
	"fmt"
	"strings"

	"dt-common/logger"
	"dt-common/omodel"
	"redis-cmanager/library/renderer"
)

// 更新 deployment.spec，修改redis的maxmemory和pod mem
// 1、新申请的pod会是pod mem的规格，存量pod不会受影响
// 2、maxmemory
func MemoryScaling(stageId int64, clusterName string, shardSize int) error {
	// 1、修改spec
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		logger.Error("[STAGE %d] failed to get deployment from db", stageId, err)
		return err
	}

	// 初始化巡检状态
	deployment.InspectionMode = omodel.MODE_FULL_CARE                     // 不改成fullCare不会触发渲染
	deployment.InspectionResult.State = renderer.INSPECTION_STATE_INIT    // 改成""用于检测渲染结果
	deployment.InspectionResult.StepErrTimes = [renderer.TOTAL_STEP]int{} // 初始化阶段状态

	// 更新deployement，已经修改过的不重复创建
	podMemory := shardSize * 1024 * 2
	if deployment.Spec.Redis.Resource.Mem == podMemory {
		err = renderer.UpdateDeploymentMode(clusterName, deployment.InspectionMode)
		if err != nil {
			logger.Error("[STAGE %d] failed to change mode to fullCare, error=(%v)", stageId, err)
			return err
		}

		err := renderer.UpdateDeploymentResult(clusterName, deployment.InspectionResult)
		if err != nil {
			logger.Error("[STAGE %d] failed to init inspection results, error=(%v)", stageId, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "cluster %s maxmemory already changed to %dGB", clusterName, shardSize)
	} else {
		// 修改maxmemory和Resource.Mem
		deployment.Spec.Redis.Resource.Mem = podMemory
		for i, config := range deployment.Spec.Redis.CustomConfig {
			if strings.HasPrefix(config, "maxmemory") {
				deployment.Spec.Redis.CustomConfig[i] = fmt.Sprintf("maxmemory %vGB", shardSize)
			}
		}

		err = renderer.SaveNewDeployment(deployment)
		if err != nil {
			logger.Error("[STAGE %d] failed to save new deployment to db, error=(%v)", stageId, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "cluster %s maxmemory changed to %dGB", clusterName, shardSize)
	}

	return nil
}

// 替换从库，支持重入
// 1、规格不匹配的从库打污点
// 2、等新从库拉起来后删除掉污点从库
func ReplaceSlaves(stageId int64, clusterName string) error {
	// 1、给从库打污点
	// err := renderer.LabelRedisToReplace(clusterName)
	// if err != nil {
	// 	return err
	// }
	// omodel.StageAppendInfoLog(stageId, "cluster %s enabledAz already changed to %v", clusterName, az)

	// 2、重启render
	// err = renderer.InitDeploymentRender(clusterName)
	// if err != nil {
	// 	logger.Error("[STAGE %d] failed to init inspection results, error=(%v)", stageId, err)
	// 	return err
	// }
	// omodel.StageAppendInfoLog(stageId, "cluster %s enabledAz already changed to %v", clusterName, az)

	// 等新pods ready,
	// err = WaitForRenderSuccess(stageId, clusterName)
	// if err != nil {
	// 	logger.Error("[STAGE %d] failed to init inspection results, error=(%v)", stageId, err)
	// 	return err
	// }
	// omodel.StageAppendInfoLog(stageId, "cluster %s enabledAz already changed to %v", clusterName, az)

	// 二次检查ready，遍历masters，检查info中的connected_slaves数量


	// 删除pod
	// err = renderer.DeleteTaintedPods(clusterName, common.COMPONENT_REDIS)
	// if err != nil {
	// 	return err
	// }

	return nil
}
