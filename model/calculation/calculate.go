package calculation

import (
	"errors"
	"fmt"
	"time"

	"sla-service/library/ent"
	"sla-service/library/ent/calculation"
	"sla-service/library/ent/event"
	"sla-service/library/ent/predicate"
	"sla-service/library/ent/service"
	"sla-service/library/ent/sla"
	"sla-service/library/ent/sli"
	"sla-service/library/logger"
	"sla-service/library/mysql"
)

const (
	DenominatorSourceEvent    = "event"
	DenominatorSourceMinutes  = "time_minutes"
	DenominatorSource5Minutes = "time_5minutes"

	ComparisonGT  = "gt"
	ComparisonGTE = "gte"
	ComparisonLT  = "lt"
	ComparisonLTE = "lte"

	PeriodMonthly   = "monthly"
	PeriodQuarterly = "quarterly"
	PeriodYearly    = "yearly"
)

// SLI 单个SLI信息
type SLI struct {
	ID                int64
	Name              string
	DenominatorSource string  // 分母数据来源：event/time_minutes/time_5minutes
	Comparison        string  // 比较方式：gt/gte/lt/lte
	Threshold         float64 // 阈值
	DecimalPlaces     int     // 数据展示时保留的小数位数
}

// 单个服务信息
type Service struct {
	ID   int64
	Name string
}

type SLA struct {
	ID            int64
	Name          string
	StartOfPeriod time.Time  // SLA周期开始时间，用来设置event获取范围
	EndOfDay      time.Time  // 当天结束时间：用来设置event获取范围
	EndOfPeriod   time.Time  // SLA周期结束时间，用来计算时间类型的分母
	Services      []*Service // 所属服务列表
	SLIs          []*SLI     // 所属SLI列表
}

// Product 产品信息
type Product struct {
	ID   int64  // 产品ID
	Name string // 产品名称
	SLAs []*SLA // 所属SLA列表
}

//
//
//

// 根据SLA周期类型计算各周期的开始和结束时间
func getStartAndEndTime(now time.Time, period string) (startOfPeriod, endOfDay, endOfPeriod time.Time) {
	// 设置当天23:59:59为结束时间
	nextDay := now.AddDate(0, 0, 1)
	endOfDay = time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 0, 0, 0, 0, now.Location())

	switch period {
	case PeriodYearly: // 年度周期
		startOfPeriod = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
		endOfPeriod = startOfPeriod.AddDate(1, 0, 0) // 明年初
	case PeriodQuarterly: // 季度周期
		quarter := (int(now.Month()) - 1) / 3
		firstMonthOfQuarter := time.Month(quarter*3 + 1)
		startOfPeriod = time.Date(now.Year(), firstMonthOfQuarter, 1, 0, 0, 0, 0, now.Location())
		endOfPeriod = startOfPeriod.AddDate(0, 3, 0) // 下个季度初
	case PeriodMonthly: // 月度周期
		startOfPeriod = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		endOfPeriod = startOfPeriod.AddDate(0, 1, 0) // 下个月初
	default: // 默认使用月度周期
		logger.Error("[calPeriodStartAndEndTime] 未知的SLA周期类型: %s", period)
		startOfPeriod = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		endOfPeriod = startOfPeriod.AddDate(0, 1, 0) // 下个月初
	}

	return
}

// 获取SLA下所有服务在周期内的所有event
func getEventList(slaId int64, startTime time.Time, endTime time.Time, svcFilter []int64, sliFilter []int64) ([]*ent.Event, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("获取数据库客户端失败: %v", err)
		return nil, err
	}

	conds := []predicate.Event{event.SLAID(slaId), event.HappenedAtGTE(startTime), event.HappenedAtLT(endTime)}
	if len(svcFilter) != 0 {
		conds = append(conds, event.ServiceIDIn(svcFilter...))
	}
	if len(sliFilter) != 0 {
		conds = append(conds, event.SliIDIn(sliFilter...))
	}

	// 一次性查询该服务在周期内的所有event数据
	ctx, cancel := mysql.ContextWithTimeout()
	events, err := db.Event.Query().Where(conds...).All(ctx)
	cancel()
	if err != nil && !ent.IsNotFound(err) {
		logger.Error("查询服务事件数据失败: %v", err)
		return nil, err
	}

	return events, nil
}

//
//
//

type CalRet struct {
	Count       int     // 参与计算的事件数量
	Numerator   float64 // 分子
	Denominator float64 // 分母
}

// 使用预先查询的events数据计算指定服务的指定SLI在指定周期内的SLA
func Daily(sla *SLA) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("获取数据库客户端失败: %v", err)
		return err
	}

	// 只查询*SLA结构体内的Service和SLI
	svcFilter, sliFilter := []int64{}, []int64{}
	for _, service := range sla.Services {
		svcFilter = append(svcFilter, service.ID)
	}
	for _, sli := range sla.SLIs {
		sliFilter = append(sliFilter, sli.ID)
	}

	// 查询SLA下的所有服务在周期内的所有event数据
	events, err := getEventList(sla.ID, sla.StartOfPeriod, sla.EndOfDay, svcFilter, sliFilter)
	if err != nil {
		return err
	}

	// 按service-sli分组，分别计算分子、分母值
	eventMap := make(map[string]*CalRet)
	for _, evt := range events {
		key := fmt.Sprintf("%d-%d", evt.ServiceID, evt.SliID)
		ret, ok := eventMap[key]
		if !ok {
			ret = &CalRet{Count: 0, Numerator: 0, Denominator: 0}
			eventMap[key] = ret // 添加这行
		}

		ret.Numerator += evt.Numerator
		ret.Denominator += evt.Denominator
		ret.Count += 1
	}

	// 遍历服务下的每个SLI，使用分组后的event数据计算SLA
	for _, service := range sla.Services {
		for _, sli := range sla.SLIs {
			var count int
			var numerator, denominator float64
			key := fmt.Sprintf("%d-%d", service.ID, sli.ID)
			if ret, ok := eventMap[key]; ok {
				count = ret.Count
				numerator = ret.Numerator
				denominator = ret.Denominator
			}

			// 时间类型分母使用当月/季度/年度的总时间（固定值）
			switch sli.DenominatorSource {
			case DenominatorSourceEvent:
			case DenominatorSourceMinutes:
				// 分母为周期总分钟数
				denominator = sla.EndOfPeriod.Sub(sla.StartOfPeriod).Minutes()
			case DenominatorSource5Minutes:
				// 分母为周期总秒数除以300（5分钟），分子从秒转换为5分钟
				denominator = sla.EndOfPeriod.Sub(sla.StartOfPeriod).Minutes() / 5
			default:
				return fmt.Errorf("未知的分母数据来源: %s", sli.DenominatorSource)
			}

			// 计算SLA值（百分比）
			var slaValue float64
			if denominator > 0 {
				slaValue = (1 - numerator/denominator) * 100
			}

			// 判断是否违反阈值
			breached := false
			switch sli.Comparison {
			case "gt":
				breached = slaValue <= sli.Threshold
			case "gte":
				breached = slaValue < sli.Threshold
			case "lt":
				breached = slaValue >= sli.Threshold
			case "lte":
				breached = slaValue > sli.Threshold
			}

			// 避免on duplicate使自增id无限放大，改为直接update
			ctx, cancel := mysql.ContextWithTimeout()
			count, err = db.Calculation.Update().
				SetNumerator(numerator).SetDenominator(denominator).
				SetValue(slaValue).SetBreached(breached).SetEventCount(count).
				Where(
					calculation.ServiceID(service.ID),
					calculation.SliID(sli.ID),
					calculation.EndTime(sla.EndOfDay),
				).
				Save(ctx)
			cancel()
			if err != nil {
				logger.Warn("failed to update calculation, product=%s, service=%s, error=(%v)", err)
				continue
			}
			// 如果没有更新到任何记录，则说明之前没有计算过，改为插入一条新的记录
			if count == 0 {
				ctx, cancel := mysql.ContextWithTimeout()
				err := db.Calculation.Create().
					SetServiceID(service.ID).SetServiceName(service.Name).
					SetSliID(sli.ID).SetSliName(sli.Name).
					SetStartTime(sla.StartOfPeriod).SetEndTime(sla.EndOfDay).
					SetNumerator(numerator).SetDenominator(denominator).
					SetValue(slaValue).SetBreached(breached).
					SetEventCount(count).
					Exec(ctx)
				cancel()
				if err != nil {
					logger.Warn("failed to create calculation, product=%s, service=%s, error=(%v)", err)
					continue
				}
			}
			logger.Info("[JobCalculateDailySLA] 服务 %s/%s 计算完成", service.Name, sli.Name)
		}
	}

	return nil
}

//
//
//

// 根据product、service、SLI获取对应SLA结构
// 事件上报时使用
func GetSLA(productName string, serviceName string, sliName string, date time.Time) (*SLA, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("获取数据库客户端失败: %v", err)
		return nil, err
	}

	sliCond := []predicate.SLI{}
	if sliName != "" {
		sliCond = append(sliCond, sli.Name(sliName))
	}

	ctx, cancel := mysql.ContextWithTimeout()
	svc, err := db.Service.Query().Where(
		service.ProductName(productName),
		service.Name(serviceName),
	).WithSLA(func(s *ent.SLAQuery) {
		s.WithSlis(func(s *ent.SLIQuery) {
			s.Where(sliCond...)
		})
	}).Only(ctx)
	cancel()
	if err != nil {
		logger.Error("获取SLA失败: %v", err)
		return nil, err
	}
	if sliName == "" && len(svc.Edges.SLA.Edges.Slis) != 1 {
		return nil, errors.New("sliName为空时，服务SLI数量必须为1")
	}

	// 计算周期开始和结束时间
	startOfPeriod, endOfDay, endOfPeriod := getStartAndEndTime(date, svc.Edges.SLA.Period)
	slaObj := &SLA{
		ID:            svc.Edges.SLA.ID,
		Name:          svc.Edges.SLA.Name,
		StartOfPeriod: startOfPeriod,
		EndOfDay:      endOfDay,
		EndOfPeriod:   endOfPeriod,
		Services:      []*Service{{ID: svc.ID, Name: svc.Name}},
	}

	// 为每个SLA创建SLI并添加到对应的SLA中
	for _, sli := range svc.Edges.SLA.Edges.Slis {
		slaObj.SLIs = append(slaObj.SLIs, &SLI{
			ID:                sli.ID,
			Name:              sli.Name,
			DenominatorSource: sli.DenominatorSource,
			Comparison:        sli.Comparison,
			Threshold:         sli.Threshold,
			DecimalPlaces:     sli.DecimalPlaces,
		})
	}

	return slaObj, nil
}

// 获取所有产品及其服务和SLI信息
func GetProductList(date time.Time, productNames ...string) ([]*Product, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("获取数据库客户端失败: %v", err)
		return nil, err
	}

	conds := []predicate.SLA{sla.StatusEQ("normal")}
	if len(productNames) != 0 {
		conds = append(conds, sla.ProductNameIn(productNames...))
	}

	ctx, cancel := mysql.ContextWithTimeout()
	slas, err := db.SLA.Query().
		Where(conds...).
		WithSlis().WithServices().
		All(ctx)
	cancel()
	if err != nil {
		logger.Error("获取SLA列表失败: %v", err)
		return nil, err
	}

	// 产品数组转Map
	productMap := make(map[int64]*Product)
	for _, sla := range slas {
		product, ok := productMap[sla.ProductID]
		if !ok {
			product = &Product{ID: sla.ProductID, Name: sla.ProductName, SLAs: []*SLA{}}
			productMap[sla.ProductID] = product
		}

		// 计算周期开始和结束时间
		startOfPeriod, endOfDay, endOfPeriod := getStartAndEndTime(date, sla.Period)
		slaObj := &SLA{
			ID:            sla.ID,
			Name:          sla.Name,
			StartOfPeriod: startOfPeriod,
			EndOfDay:      endOfDay,
			EndOfPeriod:   endOfPeriod,
		}

		// 为每个SLA创建SLI并添加到对应的SLA中
		for _, sli := range sla.Edges.Slis {
			slaObj.SLIs = append(slaObj.SLIs, &SLI{
				ID:                sli.ID,
				Name:              sli.Name,
				DenominatorSource: sli.DenominatorSource,
				Comparison:        sli.Comparison,
				Threshold:         sli.Threshold,
				DecimalPlaces:     sli.DecimalPlaces,
			})
		}

		// 为每个SLA创建Service并添加到对应的产品中
		for _, svc := range sla.Edges.Services {
			slaObj.Services = append(slaObj.Services, &Service{ID: svc.ID, Name: svc.Name})
		}

		product.SLAs = append(product.SLAs, slaObj)
	}

	// 将productMap转换为[]*Product
	result := make([]*Product, 0, len(productMap))
	for _, product := range productMap {
		result = append(result, product)
	}

	return result, nil
}
