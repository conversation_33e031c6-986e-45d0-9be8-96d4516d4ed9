package calculation

import (
	"context"
	"testing"
	"time"

	"sla-service/env"
	"sla-service/library/ent"
	"sla-service/library/mysql"
)

func Test_getStartAndEndTime(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		time   time.Time
		period string
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, time.Time, time.Time, time.Time)
	}{
		{
			name: "月度周期测试",
			args: args{
				time:   time.Date(2025, 7, 14, 22, 10, 33, 0, time.UTC),
				period: PeriodMonthly,
			},
			expect: func(t *testing.T, startOfPeriod, endOfDay, endOfPeriod time.Time) {
				if !endOfDay.Equal(time.Date(2025, 7, 15, 0, 0, 0, 0, time.UTC)) {
					t.<PERSON>rrorf("End of day should be the next day at 00:00:00, got %v", endOfDay)
				}
				if !startOfPeriod.Equal(time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC)) {
					t.<PERSON><PERSON>("Start of period should be the first day of the month, got %v", startOfPeriod)
				}
				if !endOfPeriod.Equal(time.Date(2025, 8, 1, 0, 0, 0, 0, time.UTC)) {
					t.Errorf("End of period should be the first day of next month, got %v", endOfPeriod)
				}
			},
		},
		{
			name: "季度周期测试",
			args: args{
				time:   time.Date(2025, 7, 14, 22, 10, 33, 0, time.UTC),
				period: PeriodQuarterly,
			},
			expect: func(t *testing.T, startOfPeriod, endOfDay, endOfPeriod time.Time) {
				if !startOfPeriod.Equal(time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC)) {
					t.Errorf("Start of period should be the first day of the quarter, got %v", startOfPeriod)
				}
				if !endOfPeriod.Equal(time.Date(2025, 10, 1, 0, 0, 0, 0, time.UTC)) {
					t.Errorf("End of period should be the first day of next quarter, got %v", endOfPeriod)
				}
			},
		},
		{
			name: "年度周期测试",
			args: args{
				time:   time.Date(2025, 7, 14, 22, 10, 33, 0, time.UTC),
				period: PeriodYearly,
			},
			expect: func(t *testing.T, startOfPeriod, endOfDay, endOfPeriod time.Time) {
				if !startOfPeriod.Equal(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)) {
					t.Errorf("Start of period should be the first day of the year, got %v", startOfPeriod)
				}
				if !endOfPeriod.Equal(time.Date(2026, 1, 1, 0, 0, 0, 0, time.UTC)) {
					t.Errorf("End of period should be the first day of next year, got %v", endOfPeriod)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			startOfPeriod, endOfDay, endOfPeriod := getStartAndEndTime(tt.args.time, tt.args.period)
			if tt.expect != nil {
				tt.expect(t, startOfPeriod, endOfDay, endOfPeriod)
			}
		})
	}
}

func Test_getEventList(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		slaId     int64
		startTime time.Time
		endTime   time.Time
		svcFilter []string
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, []*ent.Event, error)
	}{
		{
			name: "空记录",
			args: args{
				slaId:     1,
				startTime: time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC),
				endTime:   time.Date(2025, 7, 31, 23, 59, 59, 0, time.UTC),
			},
			expect: func(t *testing.T, events []*ent.Event, err error) {
				if err != nil {
					t.Errorf("getEventList() error = %v", err)
					return
				}
				// 验证返回的事件列表
				if events == nil {
					t.Error("getEventList() returned nil")
					return
				}
				if len(events) != 0 {
					t.Errorf("getEventList() returned %d events, want 0", len(events))
				}
			},
		},
		{
			name: "正常查询事件列表",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(1).SetProductName("Redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("time_minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("loan_risk_plms").SetStatus("normal").SaveX(context.Background())

				// 准备事件数据
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetServiceName("usercenter").SetSliID(1).SetEventType("machine_down").SetNumerator(10).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 1, 0, 5, 24, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetServiceName("usercenter").SetSliID(1).SetEventType("machine_down").SetNumerator(20).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 5, 12, 16, 54, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetServiceName("usercenter").SetSliID(1).SetEventType("machine_down").SetNumerator(30).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 24, 18, 10, 30, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(2).SetServiceName("loan_risk_plms").SetSliID(1).SetEventType("machine_down").SetNumerator(10).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 1, 0, 5, 24, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(2).SetServiceName("loan_risk_plms").SetSliID(1).SetEventType("machine_down").SetNumerator(20).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 5, 12, 16, 54, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(2).SetServiceName("loan_risk_plms").SetSliID(1).SetEventType("machine_down").SetNumerator(30).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 24, 18, 10, 30, 0, time.UTC)).SaveX(context.Background())
			},
			args: args{
				slaId:     1,
				startTime: time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC),
				endTime:   time.Date(2025, 7, 31, 23, 59, 59, 0, time.UTC),
			},
			expect: func(t *testing.T, events []*ent.Event, err error) {
				if err != nil {
					t.Errorf("getEventList() error = %v", err)
					return
				}
				// 验证返回的事件列表
				if events == nil {
					t.Error("getEventList() returned nil")
					return
				}

				if len(events) != 6 {
					t.Errorf("getEventList() returned %d events, want 3", len(events))
				}
			},
		},
		{
			name: "filter",
			args: args{
				slaId:     1,
				startTime: time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC),
				endTime:   time.Date(2025, 7, 31, 23, 59, 59, 0, time.UTC),
				svcFilter: []string{"loan_risk_plms"},
			},
			expect: func(t *testing.T, events []*ent.Event, err error) {
				if err != nil {
					t.Errorf("getEventList() error = %v", err)
					return
				}
				// 验证返回的事件列表
				if events == nil {
					t.Error("getEventList() returned nil")
					return
				}

				if len(events) != 3 {
					t.Errorf("getEventList() returned %d events, want 3", len(events))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			got, err := getEventList(tt.args.slaId, tt.args.startTime, tt.args.endTime, tt.args.svcFilter...)
			if tt.expect != nil {
				tt.expect(t, got, err)
			}
		})
	}
}

func TestDaily(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		sla       *SLA
		svcFilter []string
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, error)
	}{
		{
			name: "时间类型分母SLA计算（截止26号，不破SLA）",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(1).SetProductName("Redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("time_minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("loan_risk_plms").SetStatus("normal").SaveX(context.Background())

				// 准备事件数据, 按9999SLA计算，7月最多故障4.464分钟，26号不破SLA，27号破SLA
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetServiceName("usercenter").SetSliID(1).SetEventType("machine_down").SetNumerator(2.1).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 1, 0, 5, 24, 0, time.Now().Location())).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetServiceName("usercenter").SetSliID(1).SetEventType("machine_down").SetNumerator(2.2).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 5, 12, 16, 54, 0, time.Now().Location())).SaveX(context.Background())
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetServiceName("usercenter").SetSliID(1).SetEventType("machine_down").SetNumerator(1.2).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 27, 18, 10, 30, 0, time.Now().Location())).SaveX(context.Background())

				db.Calculation.Delete().ExecX(context.Background())
			},
			args: args{
				sla: &SLA{
					ID:            1,
					Name:          "不破SLA",
					StartOfPeriod: time.Date(2025, 7, 1, 0, 0, 0, 0, time.Now().Location()),  // 月初
					EndOfDay:      time.Date(2025, 7, 27, 0, 0, 0, 0, time.Now().Location()), // 当天
					EndOfPeriod:   time.Date(2025, 8, 1, 0, 0, 0, 0, time.Now().Location()),  // 月末
					Services: []*Service{
						{ID: 1, Name: "usercenter"},
						{ID: 2, Name: "loan_risk_plms"},
					},
					SLIs: []*SLI{
						{
							ID:                1,
							Name:              "服务可用性",
							DenominatorSource: DenominatorSourceMinutes,
							Comparison:        ComparisonGTE,
							Threshold:         99.99,
							DecimalPlaces:     2,
						},
					},
				},
			},
			expect: func(t *testing.T, err error) {
				if err != nil {
					t.Errorf("calculateSLA() error = %v", err)
					return
				}

				db, _ := mysql.Database()
				calculations, err := db.Calculation.Query().All(context.Background())
				if err != nil {
					t.Errorf("Failed to query calculations: %v", err)
					return
				}

				// 验证计算结果的数量：2个服务 × 1个SLI = 2个计算结果
				if len(calculations) != 2 {
					t.Errorf("calculateSLA() returned %d calculations, want %d", len(calculations), 2)
				}
				// 两个集群都不应该破sla
				if calculations[0].Breached != calculations[1].Breached {
					t.Errorf("calculateSLA() returned breached = %v, want false", calculations[0].Breached)
				}
			},
		},
		{
			name: "时间类型分母SLA计算（截止27号，usercenter Breached）",
			args: args{
				sla: &SLA{
					ID:            1,
					Name:          "不破SLA",
					StartOfPeriod: time.Date(2025, 7, 1, 0, 0, 0, 0, time.Now().Location()),  // 月初
					EndOfDay:      time.Date(2025, 7, 28, 0, 0, 0, 0, time.Now().Location()), // 当天
					EndOfPeriod:   time.Date(2025, 8, 1, 0, 0, 0, 0, time.Now().Location()),  // 月末
					Services: []*Service{
						{ID: 1, Name: "usercenter"},
						{ID: 2, Name: "loan_risk_plms"},
					},
					SLIs: []*SLI{
						{
							ID:                1,
							Name:              "服务可用性",
							DenominatorSource: DenominatorSourceMinutes,
							Comparison:        ComparisonGTE,
							Threshold:         99.99,
							DecimalPlaces:     2,
						},
					},
				},
			},
			expect: func(t *testing.T, err error) {
				if err != nil {
					t.Errorf("calculateSLA() error = %v", err)
					return
				}

				db, _ := mysql.Database()
				calculations, err := db.Calculation.Query().All(context.Background())
				if err != nil {
					t.Errorf("Failed to query calculations: %v", err)
					return
				}

				// 验证计算结果的数量：2个服务 × 1个SLI = 2个计算结果
				if len(calculations) != 4 {
					t.Errorf("calculateSLA() returned %d calculations, want %d", len(calculations), 2)
					return
				}
				if !calculations[2].Breached {
					t.Errorf("calculateSLA() returned breached = %v, want true", calculations[2].Breached)
					return
				}
			},
		},
		{
			name: "时间类型分母SLA重新计算（截止27号，不破SLA）",
			before: func() {
				db, _ := mysql.Database()
				db.Event.Create().SetSLAID(1).SetServiceID(1).SetServiceName("usercenter").SetSliID(1).SetEventType("fix").SetNumerator(-1.2).SetDenominator(0).SetHappenedAt(time.Date(2025, 7, 27, 18, 10, 30, 0, time.Now().Location())).SaveX(context.Background())
			},
			args: args{
				sla: &SLA{
					ID:            1,
					Name:          "不破SLA",
					StartOfPeriod: time.Date(2025, 7, 1, 0, 0, 0, 0, time.Now().Location()),  // 月初
					EndOfDay:      time.Date(2025, 7, 28, 0, 0, 0, 0, time.Now().Location()), // 当天
					EndOfPeriod:   time.Date(2025, 8, 1, 0, 0, 0, 0, time.Now().Location()),  // 月末
					Services: []*Service{
						{ID: 1, Name: "usercenter"},
						{ID: 2, Name: "loan_risk_plms"},
					},
					SLIs: []*SLI{
						{
							ID:                1,
							Name:              "服务可用性",
							DenominatorSource: DenominatorSourceMinutes,
							Comparison:        ComparisonGTE,
							Threshold:         99.99,
							DecimalPlaces:     2,
						},
					},
				},
				svcFilter: []string{"usercenter"},
			},
			expect: func(t *testing.T, err error) {
				if err != nil {
					t.Errorf("calculateSLA() error = %v", err)
					return
				}

				db, _ := mysql.Database()
				calculations, err := db.Calculation.Query().All(context.Background())
				if err != nil {
					t.Errorf("Failed to query calculations: %v", err)
					return
				}

				// 验证计算结果的数量：2个服务 × 1个SLI = 2个计算结果
				if len(calculations) != 4 {
					t.Errorf("calculateSLA() returned %d calculations, want %d", len(calculations), 2)
					return
				}
				if calculations[2].Breached {
					t.Errorf("calculateSLA() returned breached = %v, want true", calculations[2].Breached)
					return
				}
			},
		},
		{
			name: "事件类型分母SLA计算",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("noah").SetAlias("Noah").SetOwner("yuanzaiping_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(2).SetProductName("Noah").SetName("系统可用性").SetPeriod("quarterly").SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(2).SetSLAName("系统可用性").SetName("服务可用性").SetUnit("%").SetNumeratorName("pv_lost").SetDenominatorName("pv_total").SetDenominatorSource("event").SetComparison("gte").SetThreshold(99.8).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(2).SetProductName("Noah").SetSLAID(2).SetSLAName("系统可用性").SetName("监控系统").SetStatus("normal").SaveX(context.Background())

				// 准备事件数据
				db.Event.Create().SetSLAID(2).SetServiceID(3).SetSliID(2).SetEventType("report").SetNumerator(23).SetDenominator(2729).SetHappenedAt(time.Date(2025, 7, 1, 0, 5, 24, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(2).SetServiceID(3).SetSliID(2).SetEventType("report").SetNumerator(22).SetDenominator(2233).SetHappenedAt(time.Date(2025, 7, 5, 12, 16, 54, 0, time.UTC)).SaveX(context.Background())
				db.Event.Create().SetSLAID(2).SetServiceID(3).SetSliID(2).SetEventType("report").SetNumerator(17).SetDenominator(2401).SetHappenedAt(time.Date(2025, 7, 24, 18, 10, 30, 0, time.UTC)).SaveX(context.Background())

				db.Calculation.Delete().ExecX(context.Background())
			},
			args: args{
				sla: &SLA{
					ID:            2,
					Name:          "系统可用性",
					StartOfPeriod: time.Date(2025, 7, 1, 0, 0, 0, 0, time.Now().Location()),  // 月初
					EndOfDay:      time.Date(2025, 7, 28, 0, 0, 0, 0, time.Now().Location()), // 当天
					EndOfPeriod:   time.Date(2025, 8, 1, 0, 0, 0, 0, time.Now().Location()),  // 月末
					Services: []*Service{
						{ID: 3, Name: "监控系统"},
					},
					SLIs: []*SLI{
						{
							ID:                2,
							Name:              "服务可用性",
							DenominatorSource: DenominatorSourceEvent,
							Comparison:        ComparisonGTE,
							Threshold:         99.8,
							DecimalPlaces:     2,
						},
					},
				},
			},
			expect: func(t *testing.T, err error) {
				if err != nil {
					t.Errorf("calculateSLA() error = %v", err)
					return
				}

				db, _ := mysql.Database()
				calculations, err := db.Calculation.Query().All(context.Background())
				if err != nil {
					t.Errorf("Failed to query calculations: %v", err)
					return
				}

				if len(calculations) != 1 {
					t.Errorf("calculateSLA() returned %d calculations, want %d", len(calculations), 1)
				}
				if !calculations[0].Breached {
					t.Errorf("calculateSLA() returned breached = %v, want true", calculations[0].Breached)
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Daily(tt.args.sla, tt.args.svcFilter...)
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestGetSLA(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		productName string
		serviceName string
		date        time.Time
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *SLA, error)
	}{
		{
			name: "正常获取SLA",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(1).SetProductName("redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("time_minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("loan_risk_plms").SetStatus("normal").SaveX(context.Background())
			},
			args: args{
				productName: "redis",
				serviceName: "usercenter",
				date:        time.Date(2025, 7, 28, 0, 0, 0, 0, time.Now().Location()),
			},
			expect: func(t *testing.T, sla *SLA, err error) {
				if err != nil {
					t.Errorf("GetSLA() error = %v", err)
					return
				}
				if sla.Name != "集群版SLA" {
					t.Errorf("GetSLA() returned sla name = %s, want %s", sla.Name, "集群版SLA")
					return
				}
				if len(sla.Services) != 1 {
					t.Errorf("GetSLA() returned %d services, want %d", len(sla.Services), 1)
					return
				}
				if sla.Services[0].Name != "usercenter" {
					t.Errorf("GetSLA() returned service name = %s, want %s", sla.Services[0].Name, "usercenter")
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			got, err := GetSLA(tt.args.productName, tt.args.serviceName, tt.args.date)
			if tt.expect != nil {
				tt.expect(t, got, err)
			}
		})
	}
}

func TestGetProductList(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	tests := []struct {
		name   string
		before func()
		expect func(*testing.T, []*Product, error)
	}{
		{
			name: "正常获取产品列表",
			before: func() {
				db, _ := mysql.Database()
				db.Product.Create().SetName("redis").SetAlias("Redis").SetOwner("jiayiming_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())
				db.Product.Create().SetName("noah").SetAlias("Noah").SetOwner("yuanzaiping_dxm").SetDepartment("智能云业务部").SetStatus("normal").SaveX(context.Background())

				db.SLA.Create().SetProductID(1).SetProductName("Redis").SetName("集群版SLA").SetPeriod("monthly").SetStatus("normal").SaveX(context.Background())
				db.SLA.Create().SetProductID(2).SetProductName("Noah").SetName("系统可用性").SetPeriod("quarterly").SetStatus("normal").SaveX(context.Background())

				db.SLI.Create().SetSLAID(1).SetSLAName("集群版SLA").SetName("服务可用性").SetUnit("%").SetNumeratorName("downtime_minutes").SetDenominatorName("total_minutes").SetDenominatorSource("time_minutes").SetComparison("gte").SetThreshold(99.99).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())
				db.SLI.Create().SetSLAID(2).SetSLAName("系统可用性").SetName("服务可用性").SetUnit("%").SetNumeratorName("pv_lost").SetDenominatorName("pv_total").SetDenominatorSource("event").SetComparison("gte").SetThreshold(99.8).SetDecimalPlaces(2).SetStatus("normal").SaveX(context.Background())

				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("usercenter").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(1).SetProductName("Redis").SetSLAID(1).SetSLAName("集群版SLA").SetName("loan_risk_plms").SetStatus("normal").SaveX(context.Background())
				db.Service.Create().SetProductID(2).SetProductName("Noah").SetSLAID(2).SetSLAName("系统可用性").SetName("监控系统").SetStatus("normal").SaveX(context.Background())
			},
			expect: func(t *testing.T, products []*Product, err error) {
				if err != nil {
					t.Errorf("getProductList() error = %v", err)
					return
				}

				// 验证返回的产品列表结构
				if products == nil {
					t.Error("getProductList() returned nil")
					return
				}

				// 验证产品列表的基本结构
				t.Logf("获取到 %d 个产品", len(products))

				// 应该有2个产品：Redis和Noah
				if len(products) != 2 {
					t.Errorf("期望获取到2个产品，实际获取到%d个", len(products))
					return
				}

				// 验证产品信息
				productMap := make(map[string]*Product)
				for _, product := range products {
					if product.ID == 0 {
						t.Error("Product ID should not be zero")
					}
					if product.Name == "" {
						t.Error("Product Name should not be empty")
					}
					if product.SLAs == nil {
						t.Error("Product SLAs should not be nil")
					}
					productMap[product.Name] = product
					t.Logf("产品: %s (ID: %d), SLA数量: %d", product.Name, product.ID, len(product.SLAs))
				}

				// 验证Redis产品
				redisProduct, exists := productMap["Redis"]
				if !exists {
					t.Error("应该包含Redis产品")
					t.Logf("实际的产品名称: %v", func() []string {
						var names []string
						for name := range productMap {
							names = append(names, name)
						}
						return names
					}())
				} else {
					if len(redisProduct.SLAs) != 1 {
						t.Errorf("Redis产品应该有1个SLA，实际有%d个", len(redisProduct.SLAs))
					} else {
						redisSLA := redisProduct.SLAs[0]
						if redisSLA.Name != "集群版SLA" {
							t.Errorf("Redis SLA名称应该是'集群版SLA'，实际是'%s'", redisSLA.Name)
						}
						if len(redisSLA.Services) != 2 {
							t.Errorf("Redis SLA应该有2个服务，实际有%d个", len(redisSLA.Services))
						} else {
							t.Logf("Redis SLA服务: %v", []string{redisSLA.Services[0].Name, redisSLA.Services[1].Name})
						}
						if len(redisSLA.SLIs) != 1 {
							t.Errorf("Redis SLA应该有1个SLI，实际有%d个", len(redisSLA.SLIs))
						} else {
							redisSLI := redisSLA.SLIs[0]
							if redisSLI.Name != "服务可用性" {
								t.Errorf("Redis SLI名称应该是'服务可用性'，实际是'%s'", redisSLI.Name)
							}
							if redisSLI.Threshold != 99.99 {
								t.Errorf("Redis SLI阈值应该是99.99，实际是%f", redisSLI.Threshold)
							}
							t.Logf("Redis SLI: %s, 阈值: %f, 小数位数: %d", redisSLI.Name, redisSLI.Threshold, redisSLI.DecimalPlaces)
						}
					}
				}

				// 验证Noah产品
				noahProduct, exists := productMap["Noah"]
				if !exists {
					t.Error("应该包含Noah产品")
					t.Logf("实际的产品名称: %v", func() []string {
						var names []string
						for name := range productMap {
							names = append(names, name)
						}
						return names
					}())
				} else {
					if len(noahProduct.SLAs) != 1 {
						t.Errorf("Noah产品应该有1个SLA，实际有%d个", len(noahProduct.SLAs))
					} else {
						noahSLA := noahProduct.SLAs[0]
						if noahSLA.Name != "系统可用性" {
							t.Errorf("Noah SLA名称应该是'系统可用性'，实际是'%s'", noahSLA.Name)
						}
						if len(noahSLA.Services) != 1 {
							t.Errorf("Noah SLA应该有1个服务，实际有%d个", len(noahSLA.Services))
						} else {
							t.Logf("Noah SLA服务: %s", noahSLA.Services[0].Name)
						}
						if len(noahSLA.SLIs) != 1 {
							t.Errorf("Noah SLA应该有1个SLI，实际有%d个", len(noahSLA.SLIs))
						} else {
							noahSLI := noahSLA.SLIs[0]
							if noahSLI.Name != "服务可用性" {
								t.Errorf("Noah SLI名称应该是'服务可用性'，实际是'%s'", noahSLI.Name)
							}
							if noahSLI.Threshold != 99.8 {
								t.Errorf("Noah SLI阈值应该是99.8，实际是%f", noahSLI.Threshold)
							}
							if noahSLI.DenominatorSource != "event" {
								t.Errorf("Noah SLI分母来源应该是'event'，实际是'%s'", noahSLI.DenominatorSource)
							}
							t.Logf("Noah SLI: %s, 阈值: %f, 分母来源: %s", noahSLI.Name, noahSLI.Threshold, noahSLI.DenominatorSource)
						}
					}
				}

			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			got, err := GetProductList()
			if tt.expect != nil {
				tt.expect(t, got, err)
			}
		})
	}
}
