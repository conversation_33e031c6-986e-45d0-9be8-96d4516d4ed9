# Product API 文档

## 概述
产品管理API，提供产品的增删改查功能。

## API 接口

### 1. 创建产品
- **URL**: `POST /api/products`
- **描述**: 创建新产品
- **请求体**:
```json
{
    "name": "产品名称",
    "alias": "产品别名", 
    "description": "产品描述",
    "owner": "产品负责人",
    "team": "所属团队",
    "department": "所属部门"
}
```
- **响应**: 返回创建的产品信息

### 2. 获取单个产品
- **URL**: `GET /api/products/:id`
- **描述**: 根据ID获取产品详情
- **参数**: `id` - 产品ID
- **响应**: 返回产品详情

### 3. 获取产品列表
- **URL**: `GET /api/products`
- **描述**: 获取产品列表，支持分页和筛选
- **查询参数**:
  - `page`: 页码，默认1
  - `pageSize`: 每页数量，默认10，最大100
  - `name`: 产品名称（模糊匹配）
  - `alias`: 产品别名（模糊匹配）
  - `owner`: 产品负责人（模糊匹配）
  - `team`: 所属团队（模糊匹配）
  - `department`: 所属部门（模糊匹配）
- **响应**: 返回分页的产品列表

### 4. 更新产品
- **URL**: `PUT /api/products/:id`
- **描述**: 更新产品信息
- **参数**: `id` - 产品ID
- **请求体**: 同创建产品，所有字段都是可选的
- **响应**: 返回更新后的产品信息

### 5. 删除产品
- **URL**: `DELETE /api/products/:id`
- **描述**: 删除产品
- **参数**: `id` - 产品ID
- **响应**: 返回删除成功消息

## 错误码

| 错误码 | 描述 |
|--------|------|
| U2001 | 产品不存在 |
| U2002 | 产品名称已存在 |
| U2003 | 产品创建失败 |
| U2004 | 产品更新失败 |
| U2005 | 产品删除失败 |

## 示例

### 创建产品
```bash
curl -X POST http://localhost:8080/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "云服务器",
    "alias": "ECS",
    "description": "弹性云服务器",
    "owner": "张三",
    "team": "云计算团队",
    "department": "技术部"
  }'
```

### 获取产品列表
```bash
curl "http://localhost:8080/api/products?page=1&pageSize=10&name=云服务器"
```

### 更新产品
```bash
curl -X PUT http://localhost:8080/api/products/1 \
  -H "Content-Type: application/json" \
  -d '{
    "description": "更新后的产品描述",
    "owner": "李四"
  }'
```

### 删除产品
```bash
curl -X DELETE http://localhost:8080/api/products/1
``` 