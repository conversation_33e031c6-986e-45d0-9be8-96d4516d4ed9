package product

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"sla-service/library/ent"
	"sla-service/library/ent/product"
	"sla-service/library/errs"
	"sla-service/library/gintool"
	"sla-service/library/mysql"
)

// CreateProductRequest 创建产品请求
type CreateProductRequest struct {
	Name        string `json:"name" binding:"required"`  // 产品名称
	Alias       string `json:"alias" binding:"required"` // 产品别名
	Description string `json:"description"`              // 产品描述
	Owner       string `json:"owner"`                    // 产品负责人
	Team        string `json:"team"`                     // 所属团队
	Department  string `json:"department"`               // 所属部门
}

// Create 创建产品
func Create(c *gin.Context) {
	var req CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取数据库客户端
	client, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 检查产品名称是否已存在
	exists, err := client.Product.Query().Where(product.NameEQ(req.Name)).Exist(c)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	if exists {
		gintool.JSON(c, nil, errs.CodeProductNameExists.Detail())
		return
	}

	// 创建产品
	product, err := client.Product.Create().
		SetName(req.Name).
		SetAlias(req.Alias).
		SetNillableDescription(&req.Description).
		SetNillableOwner(&req.Owner).
		// SetNillableTeam(&req.Team).
		SetNillableDepartment(&req.Department).
		Save(c)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeProductCreateFailed.Detail(err.Error()))
		return
	}

	gintool.JSON(c, product, nil)
}

// UpdateProductRequest 更新产品请求
type UpdateProductRequest struct {
	Name        string `json:"name"`        // 产品名称
	Alias       string `json:"alias"`       // 产品别名
	Description string `json:"description"` // 产品描述
	Owner       string `json:"owner"`       // 产品负责人
	Team        string `json:"team"`        // 所属团队
	Department  string `json:"department"`  // 所属部门
}

// Update 更新产品
func Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("无效的产品ID"))
		return
	}

	var req UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取数据库客户端
	client, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 检查产品是否存在
	objProduct, err := client.Product.Get(c, id)
	if err != nil {
		if ent.IsNotFound(err) {
			gintool.JSON(c, nil, errs.CodeProductNotFound.Detail())
			return
		}
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 如果更新名称，检查名称是否已存在
	if req.Name != "" && req.Name != objProduct.Name {
		exists, err := client.Product.Query().
			Where(product.NameEQ(req.Name)).
			Where(product.IDNEQ(id)).
			Exist(c)
		if err != nil {
			gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
			return
		}
		if exists {
			gintool.JSON(c, nil, errs.CodeProductNameExists.Detail())
			return
		}
	}

	// 构建更新操作
	update := client.Product.UpdateOneID(id)
	if req.Name != "" {
		update.SetName(req.Name)
	}
	if req.Alias != "" {
		update.SetAlias(req.Alias)
	}
	if req.Description != "" {
		update.SetDescription(req.Description)
	}
	if req.Owner != "" {
		update.SetOwner(req.Owner)
	}
	// if req.Team != "" {
	// 	update.SetTeam(req.Team)
	// }
	if req.Department != "" {
		update.SetDepartment(req.Department)
	}

	// 执行更新
	updatedProduct, err := update.Save(c)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeProductUpdateFailed.Detail(err.Error()))
		return
	}

	gintool.JSON(c, updatedProduct, nil)
}

// Delete 删除产品
func Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("无效的产品ID"))
		return
	}

	// 获取数据库客户端
	client, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 检查产品是否存在
	exists, err := client.Product.Query().Where(product.IDEQ(id)).Exist(c)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	if !exists {
		gintool.JSON(c, nil, errs.CodeProductNotFound.Detail())
		return
	}

	// 删除产品
	err = client.Product.DeleteOneID(id).Exec(c)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeProductDeleteFailed.Detail(err.Error()))
		return
	}

	gintool.JSON(c, gin.H{"message": "产品删除成功"}, nil)
}

//
//
//

// Get 获取单个产品
func Get(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("无效的产品ID"))
		return
	}

	// 获取数据库客户端
	client, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	product, err := client.Product.Get(c, id)
	if err != nil {
		if ent.IsNotFound(err) {
			gintool.JSON(c, nil, errs.CodeProductNotFound.Detail())
			return
		}
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON(c, product, nil)
}

// List 获取产品列表
func List(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")
	name := c.Query("name")
	alias := c.Query("alias")
	owner := c.Query("owner")
	// team := c.Query("team")
	department := c.Query("department")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 获取数据库客户端
	client, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建查询条件
	query := client.Product.Query()
	if name != "" {
		query = query.Where(product.NameContains(name))
	}
	if alias != "" {
		query = query.Where(product.AliasContains(alias))
	}
	if owner != "" {
		query = query.Where(product.OwnerContains(owner))
	}
	// if team != "" {
	// 	query = query.Where(product.TeamContains(team))
	// }
	if department != "" {
		query = query.Where(product.DepartmentContains(department))
	}

	// 获取总数
	count, err := query.Count(c)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 获取分页数据
	products, err := query.
		Order(ent.Desc(product.FieldCreatedAt)).
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		All(c)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	result := gintool.FormatPaging(count, products, page, pageSize)
	gintool.JSON(c, result, nil)
}
