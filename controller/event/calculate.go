package event

import (
	"time"

	"github.com/gin-gonic/gin"

	"sla-service/library/errs"
	"sla-service/library/gintool"
	"sla-service/model/calculation"
)

// CalculateRequest 事件计算请求
type CalculateRequest struct {
	Product  string    `json:"product" binding:"required"` // 产品名称
	Datetime time.Time `json:"datetime"`                   // 事件发生时间，不填默认当前时间
}

// Calculate SLA计算
func Calculate(c *gin.Context) {
	var args CalculateRequest
	if err := c.ShouldBindJSON(&args); err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 参数校验
	if args.Product == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("product is empty"))
		return
	}
	if args.Datetime.IsZero() {
		args.Datetime = time.Now()
	}

	// 根据产品、服务获取SLA、SLI信息
	slas, err := calculation.GetProductList(args.Datetime, args.Product)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeInvalidParameter.Detail(err.Error()))
		return
	}

	for _, product := range slas {
		for _, sla := range product.SLAs {
			err = calculation.Daily(sla)
			if err != nil {
				gintool.JSON(c, nil, errs.CodeCalculateFailed.Detail(err.Error()))
				return
			}
		}
	}

	gintool.JSON(c, "ok", nil)
}
