package event

import (
	"time"

	"github.com/gin-gonic/gin"

	"sla-service/library/ent"
	"sla-service/library/ent/service"
	"sla-service/library/ent/sli"
	"sla-service/library/errs"
	"sla-service/library/gintool"
	"sla-service/library/mysql"
)

// ReportEventRequest 事件上报请求
type ReportEventRequest struct {
	Product     string    `json:"product" binding:"required"`   // 产品名称
	Service     string    `json:"service" binding:"required"`   // 服务名称
	SLI         string    `json:"sli" binding:"required"`       // SLI名称
	EventType   string    `json:"eventType" binding:"required"` // 事件类型：metric_report/incident/maintenance等
	EventName   string    `json:"eventName"`                    // 事件名称
	Description string    `json:"description,omitempty"`        // 事件描述
	Reporter    string    `json:"reporter"`                     // 上报人
	Numerator   float64   `json:"numerator" binding:"required"` // 分子值
	Denominator float64   `json:"denominator,omitempty"`        // 分母值
	StartTime   time.Time `json:"startTime"`                    // 事件开始时间
	EndTime     time.Time `json:"endTime"`                      // 事件结束时间
}

// Report 事件上报
func Report(c *gin.Context) {
	var req ReportEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取数据库客户端
	client, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 根据服务名称查找服务
	service, err := client.Service.Query().
		Where(service.NameEQ(req.Service)).
		First(c)
	if err != nil {
		if ent.IsNotFound(err) {
			gintool.JSON(c, nil, errs.CodeServiceNotFound.Detail("服务不存在: "+req.Service))
			return
		}
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 根据SLI名称查找SLI
	sli, err := client.SLI.Query().
		Where(sli.NameEQ(req.SLI)).
		First(c)
	if err != nil {
		if ent.IsNotFound(err) {
			gintool.JSON(c, nil, errs.CodeSLINotFound.Detail("SLI不存在: "+req.SLI))
			return
		}
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 验证服务和SLI是否匹配（属于同一个SLA）
	if service.SLAID != sli.SLAID {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("服务与SLI不匹配"))
		return
	}

	// 设置默认值
	if req.StartTime.IsZero() {
		req.StartTime = time.Now()
	}
	if req.EndTime.IsZero() {
		req.EndTime = time.Now()
	}

	// 创建事件
	event, err := client.Event.Create().
		SetServiceID(service.ID).
		SetServiceName(service.Name).
		SetSliID(sli.ID).
		SetSliName(sli.Name).
		SetEventType(req.EventType).
		SetEventName(req.EventName).
		SetDescription(req.Description).
		SetReporter(req.Reporter).
		SetNumerator(req.Numerator).
		SetDenominator(req.Denominator).
		SetHappenedAt(time.Now()).
		Save(c)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeEventCreateFailed.Detail(err.Error()))
		return
	}

	gintool.JSON(c, event, nil)
}
