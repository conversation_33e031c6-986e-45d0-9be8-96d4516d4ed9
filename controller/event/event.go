package event

import (
	"time"

	"github.com/gin-gonic/gin"

	"sla-service/library/errs"
	"sla-service/library/gintool"
	"sla-service/library/mysql"
	"sla-service/model/calculation"
)

// ReportEventRequest 事件上报请求
type ReportEventRequest struct {
	Product     string    `json:"product" binding:"required"`   // 产品名称
	Service     string    `json:"service" binding:"required"`   // 服务名称
	SLI         string    `json:"sli,omitempty"`                // SLI
	EventType   string    `json:"eventType" binding:"required"` // 事件类型：metric_report/incident/maintenance等
	Description string    `json:"description,omitempty"`        // 事件描述，选填
	Reporter    string    `json:"reporter"`                     // 上报人，选填
	Numerator   float64   `json:"numerator" binding:"required"` // 分子值
	Denominator float64   `json:"denominator,omitempty"`        // 分母值，时间类型选填
	HappenedAt  time.Time `json:"happenedAt"`                   // 事件发生时间，不填默认当前时间
}

// Report 事件上报
func Report(c *gin.Context) {
	var args ReportEventRequest
	if err := c.ShouldBindJSON(&args); err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 参数校验
	if args.Product == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("product is empty"))
		return
	}
	if args.Service == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("service is empty"))
		return
	}
	if args.HappenedAt.IsZero() {
		args.HappenedAt = time.Now()
	}

	// 根据产品、服务获取SLA、SLI信息
	modelSLA, err := calculation.GetSLA(args.Product, args.Service, args.SLI, args.HappenedAt)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeInvalidParameter.Detail(err.Error()))
		return
	}
	// 唯一服务&SLI
	modelService := modelSLA.Services[0]
	modelSLI := modelSLA.SLIs[0]

	// 获取数据库客户端
	client, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	// 插入事件
	ctx, cancel := mysql.ContextWithTimeout()
	event, err := client.Event.Create().
		SetSLAID(modelSLA.ID).SetSLAName(modelSLA.Name).
		SetSliID(modelSLI.ID).SetSliName(modelSLI.Name).
		SetServiceID(modelService.ID).SetServiceName(modelService.Name).
		SetEventType(args.EventType).SetDescription(args.Description).
		SetNumerator(args.Numerator).SetDenominator(args.Denominator).
		SetReporter(args.Reporter).SetHappenedAt(args.HappenedAt).
		Save(ctx)
	cancel()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeEventCreateFailed.Detail(err.Error()))
		return
	}

	// 如果事件不是发生在当天的，重新计算当日SLO
	now := time.Now()
	curDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	if args.HappenedAt.Before(curDay) {
		calculation.Daily(modelSLA)
	}

	gintool.JSON(c, event, nil)
}
