package event

import (
	"encoding/json"
	"io"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestReport(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == "00000" {
					t.Error(w.Code, string(b))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			Report(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
