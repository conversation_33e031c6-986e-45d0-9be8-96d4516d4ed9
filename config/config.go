package config

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/yaml.v2"
)

var (
	configMap map[string]any
)

func Init(path ...string) error {
	filePath := "./config/config.yaml"
	if len(path) != 0 {
		filePath = path[0]
	}

	// 读取yaml配置文件
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("read config file, %v", err)
	}

	// 反序列化
	configMap = make(map[string]any)
	err = yaml.Unmarshal(content, &configMap)
	if err != nil {
		return fmt.Errorf("config unmarshal error, %v", err)
	}

	return nil
}

// 根据key获取对应的值，如果值为struct，则继续反序列化
// 支持层级获取，以分隔符“/”划分pKey和cKey，例如：mts/bj
func Get(key string, out any) error {
	ks := strings.Split(key, "/")

	var rawConfig any
	var exists bool
	tmp := configMap
	for i := 0; i < len(ks); i++ {
		rawConfig, exists = tmp[ks[i]]
		if !exists {
			return fmt.Errorf("[config] fail to get cfg with key: %s", ks[0])
		}

		d, err := yaml.Marshal(&rawConfig)
		if err != nil {
			return fmt.Errorf("config marshal error, %v", err)
		}

		if i+1 == len(ks) {
			err = yaml.Unmarshal(d, out)
		} else {
			tmp = make(map[string]any)
			err = yaml.Unmarshal(d, &tmp)
		}
		if err != nil {
			return fmt.Errorf("config unmarshal error, key: %s", key)
		}
	}

	return nil
}

// 根据Key获取对应配置，类型为string
func GetString(key string) (string, error) {
	var s string
	err := Get(key, &s)
	if err != nil {
		return "", err
	}

	return s, nil
}

// 根据Key获取对应配置，类型为int
func GetInt(key string) (int, error) {
	var i int
	err := Get(key, &i)
	if err != nil {
		return 0, err
	}

	return i, nil
}

// 根据Key获取对应配置，类型为bool
func GetBool(key string) (bool, error) {
	var b bool
	err := Get(key, &b)
	if err != nil {
		return false, err
	}

	return b, nil
}
