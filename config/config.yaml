application:
  # debug:开发环境 test:测试环境 release:线上环境
  mode: debug
  # 服务端口号
  port: 8811
  # 接口鉴权token
  token: sla-service-123456

timer:

db:
  host: ************
  port: 8846
  name: sla
  user: normal
  password: xjHD6Ph38xsDYK3A
  # 是否进入debug模式，打印所有执行的sql语句
  orm_debug: true
  # 最大连接空置时间
  max_idle_conns: 10
  # 设置最大连接数
  max_open_conns: 100
  # 设置超时时间
  single_query_timeout: 30

logger:
  # 日志等级 [error, warn, info, debug] 默认info
  level: debug
  # 是否打印到控制台 [true, false]
  log_console: false
  # 日志存放路径
  path: log/sla-service.log
  # 在进行切割之前，日志文件的最大大小（以MB为单位）
  max_size: 20
  # 保留旧文件的最大天数
  max_age: 30
  # 保留旧文件的最大个数
  max_backups: 25
