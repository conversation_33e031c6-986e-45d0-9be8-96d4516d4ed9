# SLA Service

## 简要说明

2025/01/02 ~ 2025/01/15
+----------------------+
|  huagu_xd            |
|        99.98%        |
+----------------------+

截止到当天，本周期内的SLA

- 每天每个产品的每个服务算一个数：截止到今天23:59:59，本服务在本周期内的SLA
- event表查询范围为「截止时间所在的周期开始（通常为月初或季度初）」到「截止时间当天的23:59:59」
-

1、SLA的计算，按照起始时间月份的1号，到截止时间为止，累加分子/分母
2、分子/分母数据默认以上报的形式采集
3、没有按时间展示SLA的需求

1、redis/kafka等，分子由外部上报，分母固定为一个周期内的总分钟数，每天一个点
2、bos，分子由外部上报，分母固定为一个周期内的总5分钟数（总共有多少个5分钟），每天一个点
3、noah，分子/分母皆由外部上报，现查

### 定时计算

1、本周期内的SLA
2、当天的SLA

## 快速开始

go run -mod=mod entgo.io/ent/cmd/ent generate --feature sql/modifier,sql/execquery,sql/lock,sql/upsert ./library/ent/schema

## 测试



1、最好有代码示例做参考，可以对齐项目结构和代码风格
2、你如果不知道该怎么实现，大概率也没法让大模型很好的实现

