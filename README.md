# redis-xweb

## 项目介绍

本项目是基于gin和ent框架搭建的后端web服务，在R3项目中作为业务层起到承上（用户）启下（控制层）的作用。

## 快速启动

1. 克隆本项目；
2. `go mod tidy` 拉取依赖库；
3. 执行 `go run main.go` ，默认在8888端口运行，在浏览器中访问 <http://localhost:8400/demo/ping> 。

## 目录结构

```text
├── cmd
│      └── app
│           └──  main.go    主程序入口
├── config                  配置包
│      ├── config.go        配置文件对应的结构体对象
│      └── config.yaml      配置文件，在此修改全局配置
├── global                  全局变量包
│      └── global.go        初始化后将全局变量存放此处，可全局调用
├── controller              控制层
│      └── cluster          业务示例：redis集群包
│               └── get.go  redis集群中的get方法实现
├── library                 工具包
│      ├── auth-center      鉴权中心
│      ├── db               数据库连接
│      ├── ecode            错误码定义
│      ├── gintool          gin工具包（封装统一返回格式等）
│      └── logger           日志记录器
├── log                     日志输出目录
├── model                   model层
│      └── cluseter.go      业务示例：cluster表结构体定义
├── router                  路由层
│      └── router.go        路由注册
└── test                    测试包
```

## 功能介绍

### **主要组件**

本项目整合了如下开发API所必要的组件：

#### 1. [Gin](https://github.com/gin-gonic/gin)

一款基于Go语言的轻量级Web框架，它具有快速、高效、易用等特点。在本项目中，我们使用了Gin框架来处理HTTP请求和响应，并搭建了一套RESTful API。
使用请参考[官方文档](https://gin-gonic.com/docs/)。

#### 2. [GORM](https://gorm.io/)

一个Go语言的ORM库，支持MySQL、PostgreSQL、SQLite等数据库。在本项目中，我们使用了GORM框架来管理Mysql的存储和查询。使用请参考[官方文档](https://gorm.io/zh_CN/docs/index.html)。

#### 3. [Zap](https://github.com/uber-go/zap)

Zap是Uber开源的Go高性能日志库，提供结构化日志记录，支持不同的日志级别。其性能比类似的结构化日志包更好，也比标准库更快。
在本项目中，我们定义Zap的返回格式如下：

`[时间] [日志级别] [代码行号] 记录信息`

本项目为gin配置了logger中间件，将每次请求写入到日志中，以下是访问/v1/ping接口时到日志示例：

```text
[2023-03-23 15:58:14] [INFO] [logger/logger.go:23] msg:  {"status": 200, "method": "GET", "path": "/favicon.ico", "query": "", "ip": "::1", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36", "errors": "", "cost": 0.000099959}
```

本项目共设定了四个日志级别，由高至低分别为`Fatal`, `Warn`, `Info`和`Debug`。您可在任意位置通过调用如下方法记录日志：

```go
// 打印日志
global.logger.Fatal("msg")
global.logger.Warn("msg")
global.logger.Info("msg")
global.logger.Debug("msg")

// 此方法后可接key-value结构化参数，如下所示
global.logger.Info("msg: ",
    zap.Int("status", c.Writer.Status()),
    zap.String("method", c.Request.Method),
    zap.String("path", path),
    zap.String("query", query),
    zap.String("ip", c.ClientIP()),
    zap.String("user-agent", c.Request.UserAgent()),
    zap.String("errors", c.Errors.ByType(gin.ErrorTypePrivate).String()),
    zap.Duration("cost", cost),
)

// 如需使用printf风格打印日志，请使用如下方法：
global.logger.Fatalf("msg: %s", msg)
global.logger.Warnf("msg: %s", msg)
global.logger.Infof("msg: %s", msg)
global.logger.Debugf("msg: %s", msg)
```

#### 4. [Lumberjack](https://github.com/natefinch/lumberjack)

Zap官方推荐的日志滚动记录器。Zap本身不支持切割归档日志文件，Lumberjack与Zap搭配使用实现日志的滚动记录。
可通过修改`config/config.yaml`中的如下位置修改相关配置：

```yaml
logger:
  # 日志等级 [error, warn, info, debug] 默认info
  level: debug
  # 是否打印到控制台 [true, false]
  log_console: true
  # 日志存放路径
  path: log/web_app.log
  # 在进行切割之前，日志文件的最大大小（以MB为单位）
  max_size: 100
  # 保留旧文件的最大天数
  max_age: 30
  # 保留旧文件的最大个数
  max_backups: 10
```

### **全局对象**

`global`作为全局对象需要在程序启动时调用`global.Init()`进行初始化，初始化的组件包括：

- `config`
- `logger`
- `db/mysql`

### **配置管理**

使用`.yaml`格式文件作为配置文件，在程序启动时通过`config.Init()`加载文件进行解析。配置项需要在`config.go`文件中以`struct`的形式进行定义后才能使用。

`config.Init()`包含在`global.Init()`中，并且在初始化完成后可以通过`global.CFG`进行调用。

### **日志打印**

日志分为两类：

- 通过`log`或者`fmt`打印的日志，标准输出
- 通过`zap.Logger`打印的日志，输出到文件

程序启动通过`nohup`，标准输出会导向`log/nohup_xxx.log`文件；通过`global.Logger`打印的日志会按天切分文件保存在`log/`目录下。

### **错误处理**

错误分为两类：

- 预期的`error`
- 非预期的`panic`

#### **error**

`error`的定义在`library/ecode`中，错误类型有以下几种:

- 由用户引发的错误：`A`
- 由系统引发的错误：`S`
- 由数据库引发的错误：`M`
- 由用户中心引发的错误：`U`
- ...

错误码由错误类型+4位编号组成，例如：`S0001`

#### **panic**

`gin`框架提供`recovery`中间件用于全局捕获`panic`，通过自定义`recovery`中间件，将`panic`的堆栈信息通过`global.Logger`记录在日志文件中。

### **返回值**

通过`gintool.JSON`方法构建`response body`，标准接口返回值结构定义如下:

```go
    type Body struct{
        Code string       `json:"code"`
        Msg  string       `json:"msg"`
        Data interface{}  `json:"data"`
    }
```

### **Auth Center**

用户中心的地址需要进行初始化`authCenter.Init`

来自Cloud的前端请求默认会在`Header`中附带`Authorization`参数，解析出来并调用auth center api获取用户身份信息。

通过中间件的形式，将用户鉴权加入到请求流程中。

```go
    r.Use(gintool.AuthFilter())
```

## web请求流程

一个web请求在本项目中的完整流程：

1. 当一个web请求到达时，它会被路由层(router)接收；
2. 路由层将请求转发给相应的控制层(http)做相应的处理；
3. 控制层处理请求后，调用相关的模型(model)；（注：本项目未实现服务层(service)，由控制层直接操作model）
4. model将数据存储到数据库中，将结构返回给业务层；
5. 业务层将结果返回给客户端。

## 接口 DEMO

本项目预先实现了一些示例接口方便参考和复用：

1. `GET /demo/ping`，此接口绕过鉴权中间件，可直接在浏览器中测试；
2. `GET /demo/print-log``cluster-info`模型，对应数据库中的`cluster-info`表；
3. `GET /demo/no-api`
4. `GET /demo/panic`
5. `GET /demo/return-err`

## 其他使用技巧

### 并发控制

`errgroup`

### 分页器

`gintool.FormatPaging`

```go
    type Paging struct {
        Count     int64       `json:"count"`
        Page      int         `json:"page"`
        PageSize  int         `json:"pageSize"`
        Rows      interface{} `json:"rows"`
        TotalPage int         `json:"totalPage"`
    }

    // 分页返回格式化
    func FormatPaging() *Paging 
```
