package flowc

import (
	"encoding/json"
	"fmt"
	"time"
)

// =====================================================
//                      创建流程
// =====================================================

// 资源列表：资源类型、资源ID、资源唯一ID、资源展示名称、资源动作
// 流程资源关联
type FlowResource struct {
	ResourceType  string `json:"resourceType"`  // 资源类型
	ResourceID    int64  `json:"resourceID"`    // 资源ID，平台存储的
	UniqueId      string `json:"uniqueID"`      // 唯一值，比如主机ID i-xxx,bucket 名称等
	ResourceLabel string `json:"resourceLabel"` // 展示名称,比如 hostName、ip 等
	Action        string `json:"action"`        // create update return transfer
}

type FlowIndexType struct {
	VIP           string
	Department    int
	FlowSubmitter string
	NoahProduct   string
	InstanceId    string
	Domain        string
	BNS           string
}

type FlowCreate struct {
	ListID           int               `json:"list_id" valid:"Required" required:"true"`
	ListType         string            `json:"list_type" valid:"Required" required:"true"`
	FlowName         string            `json:"flow_name" valid:"Required" required:"true"`
	Applicant        string            `json:"applicant" valid:"Required" required:"true"`
	FlowInstanceName string            `json:"flow_instance_name" `
	FlowIndex        FlowIndexType     `json:"flow_index"`
	FlowIndexColumn  []string          `json:"flow_index_column"`
	RoleUser         map[string]string `json:"role_user"`
	DeadLine         time.Time         `json:"deadLine" description:"到期时间"`
	DeadLineStatus   string            `json:"deadLineStatus"  description:"到期状态"`
	DeadLineAction   string            `json:"deadLineAction" description:"到期执行函数"`
	FlowResources    []FlowResource    `json:"flowResources" description:"流程关联资源"`
}

type FlowCreatePostForm struct {
	Token string `json:"token" valid:"Required" required:"true"`
	AppId string `json:"app_id"`
	FlowCreate
}

type FlowCreateResult struct {
	ApproverList   []string `json:"approverList"`
	FlowInstanceId uint32   `json:"flowInstanceId"`
	NextNodeCH     string   `json:"nextNodeCH"`
}

// 创建流程
func AddListToFlow(flowCreate *FlowCreate) (*FlowCreateResult, error) {
	if flowCreate.ListID == 0 {
		return nil, fmt.Errorf("listId is required but missing")
	}
	if flowCreate.ListType == "" {
		return nil, fmt.Errorf("ListType is required but missing")
	}

	subUrl := "/flow/CreateFlowInstance"
	body := FlowCreatePostForm{
		Token:      token,
		AppId:      "",
		FlowCreate: *flowCreate,
	}

	var resp Response
	err := post(subUrl, body, &resp)
	if err != nil {
		return nil, fmt.Errorf("create flow instance error, %s", err.Error())
	}

	var ret FlowCreateResult
	err = json.Unmarshal(resp.Data, &ret)
	if err != nil {
		return nil, err
	}

	return &ret, nil
}

type FlowResubmitPostForm struct {
	Token string `json:"token" valid:"Required" required:"true"`
	AppId string `json:"app_id"`
	FlowResubmit
}

type FlowResubmit struct {
	FlowInstanceId uint32 `json:"flow_instance_id" valid:"Required" required:"true"`
	Applicant      string `json:"applicant" valid:"Required" required:"true"`
}

// 重提交resubmit
func ResubmitListToFlow(resubmit *FlowResubmit) (*FlowCreateResult, error) {
	subUrl := "/flow/ResubmitFlowInstance"
	body := FlowResubmitPostForm{
		Token:        token,
		FlowResubmit: *resubmit,
	}

	var resp Response
	err := post(subUrl, body, &resp)
	if err != nil {
		return nil, fmt.Errorf("resubmit flow instance error, %s", err.Error())
	}

	var ret FlowCreateResult
	err = json.Unmarshal(resp.Data, &ret)
	if err != nil {
		return nil, err
	}

	return &ret, nil
}

// =====================================================
//                  异步返回节点结果
// =====================================================

type AddResult struct {
	FlowInstanceId uint32 // Flow实例ID
	NodeInstanceId uint32 // Node实例ID
	Code           int    // 流程结果：成功0，失败非0
	Message        string // 节点日志
}

type ResultInfo struct {
	ErrNo    int           `json:"errno"`              //!错误码
	ErrMsg   string        `json:"errmsg"`             //!提示信息
	Data     any           `json:"data"`               //!数据
	RawErr   error         `json:"-"`                  //!原始错误
	Cost     float64       `json:"cost,omitempty"`     //!花费时间，非必填,替换
	CostTime time.Duration `json:"costTime,omitempty"` //!花费时间，非必填
}
type ProcessResult struct {
	//FuncName       string
	FlowInstanceID uint32 `json:"flow_instance_id" valid:"Required" required:"true"`
	NodeInstanceID uint32 `json:"node_instance_id" valid:"Required" required:"true"`
	CallbackMsg    map[string]any
	Result         ResultInfo `json:"result" valid:"Required" required:"true"`
}

type ProcessResultPostForm struct {
	Token string `json:"token" valid:"Required" required:"true"`
	AppID string `json:"app_id"`
	ProcessResult
}

// AddProcessResult
func AddProcessResult(request *AddResult) error {
	subUrl := "/flow/ProcessCallBack"
	body := ProcessResultPostForm{
		Token: token,
		ProcessResult: ProcessResult{
			FlowInstanceID: request.FlowInstanceId,
			NodeInstanceID: request.NodeInstanceId,
			CallbackMsg: map[string]any{
				"Subject": "[Auto]",
				"Message": request.Message,
			},
			Result: ResultInfo{
				ErrNo:  request.Code,
				ErrMsg: request.Message,
			},
		},
	}
	var resp Response
	err := post(subUrl, body, &resp)
	if err != nil {
		return fmt.Errorf("add flow process result error, %s", err.Error())
	}

	return nil
}

// =====================================================
//                    添加执行日志
// =====================================================

type Log struct {
	FlowInstanceID uint32
	NodeInstanceID uint32
	LogInfo
}

type LogInfo struct {
	StartTime string `json:"startTime"`
	Operator  string `json:"operator"`
	Operation string `json:"operation"`
	Result    string `json:"result"`
}

type NodeLog struct {
	FlowInstanceId uint32  `json:"flow_instance_id"`
	NodeInstanceId uint32  `json:"node_instance_id"`
	Log            LogInfo `json:"log"`
	Token          string  `json:"token"`
}

// 添加一段日志到流程
func AddLogToFlow(log *Log) error {
	subUrl := "/flow/AddLogToFlow"
	body := NodeLog{
		FlowInstanceId: log.FlowInstanceID,
		NodeInstanceId: log.NodeInstanceID,
		Log:            log.LogInfo,
		Token:          token,
	}
	var resp Response
	err := post(subUrl, body, &resp)
	if err != nil {
		return fmt.Errorf("add flow process result error, %s", err.Error())
	}

	return nil
}
