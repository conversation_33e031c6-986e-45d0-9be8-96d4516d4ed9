package flowc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

type Config struct {
	Server  string `yaml:"server"`
	Version string `yaml:"version"`
	Token   string `yaml:"token"`
}

var (
	host  string
	token string
)

// 初始化权限中心地址
func Init(cfg *Config) error {
	cfg.Server, _ = strings.CutSuffix(cfg.Server, "/")
	if cfg.Server == "" {
		return fmt.Errorf("config 'auth.server' missing")
	}

	if cfg.Token == "" {
		return fmt.Errorf("missing flow center config, token is empty")
	}

	if !strings.HasPrefix(cfg.Server, "http") {
		host = "http://" + cfg.Server + "/api"
	} else {
		host = cfg.Server + "/api"
	}

	token = cfg.Token

	return nil
}

// =====================================================
//                      Response
// =====================================================

// response 子系统下的app查询结果的结构体
type Response struct {
	ErrNo  int    `json:"errno"`
	ErrMsg string `json:"errmsg"`
	// 改为使用接口接收数据，因为返回结果可能是字符串
	Data json.RawMessage `json:"data"`
}

// 适用于V1版本的接口请求
func post(subUrl string, reqBody any, v ...any) error {
	if subUrl == "" {
		return fmt.Errorf("subURL is empty")
	}

	url := host + "/v1" + subUrl
	params, _ := json.Marshal(reqBody)
	reader := bytes.NewReader(params)

	client := http.Client{
		Timeout: 5 * time.Second,
	}
	resp, err := client.Post(url, "application/json", reader)
	if err != nil {
		return err
	}

	// 不需要返回值
	if v == nil {
		return nil
	}

	resBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	switch t := v[0].(type) {
	case Response:
		err = json.Unmarshal(resBody, &t)
		if err != nil {
			return err
		}
		if t.ErrNo != 0 {
			return fmt.Errorf(t.ErrMsg)
		}
	default:
		err = json.Unmarshal(resBody, &t)
		if err != nil {
			return err
		}
	}

	return nil
}
