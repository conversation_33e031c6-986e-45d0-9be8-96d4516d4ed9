package flowc

import (
	"encoding/json"
	"net/http"
)

// FuncAsyncCaller/FuncSyncCaller 入参
type FuncCall struct {
	FuncName string
	FuncType string
	Args     []any
}

// FlowFuncAsyncCaller/FlowFuncSyncCaller 入参
type FlowFuncCall struct {
	FuncName string   `json:"func_name"`
	FuncType string   `json:"func_type"`
	Args     FlowArgs `json:"args"`
}

// FuncAsyncCaller/FuncSyncCaller 函数参数
type FlowArgs struct {
	FlowInstanceId    uint32
	NodeInstanceId    uint32
	ListId            int64
	ListType          string
	NodeSubmitHistory map[string]string
	NodeReturnHistory map[string]string
}

// GetListDetail 入参
type ListInfo struct {
	ListID   int    `json:"list_id"`
	ListType string `json:"list_type"`
}

// 用于业务系统格式化给流程中心的返回值
func Format(data any, err error) (int, *Response) {
	var b Response
	var httpStatus int

	if err != nil {
		b.ErrNo = 1
		b.ErrMsg = err.Error()
		httpStatus = http.StatusBadRequest
	} else if data == nil {
		b.ErrNo = 1
		b.ErrMsg = "no return value"
		httpStatus = http.StatusInternalServerError
	} else {
		b.ErrNo = 0
		httpStatus = http.StatusOK
		b.Data, err = json.Marshal(data)
		if err != nil {
			b.ErrNo = 1
			httpStatus = http.StatusInternalServerError
		}
	}

	return httpStatus, &b
}
