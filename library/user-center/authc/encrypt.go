package authc

import (
	"crypto/sha256"

	"encoding/json"
	"fmt"
	"sort"
	"strconv"
)

// sha256Sign 加密签名
func Sha256Sign(paramsStr string) (signStr string) {
	h := sha256.New()
	h.Write([]byte(paramsStr))
	signStr = fmt.Sprintf("%x", h.Sum(nil))
	return
}

// sortValueByKey 按照字典的key 来排序value
func SortValueByKey(params map[string]any) (sortedValue string) {
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		switch params[k].(type) {
		case string:
			sortedValue += params[k].(string)
		case int:
			sortedValue += strconv.Itoa(params[k].(int))
		case float64:
			sortedValue += fmt.Sprintf("%.0f", params[k])
		default:
			value, _ := json.<PERSON>(params[k])
			sortedValue += string(value)
		}
	}
	return
}

// 结构体转map
func StructToMap(obj any) map[string]any {
	jsonBytes, _ := json.Marshal(obj)
	var data = make(map[string]any)
	json.Unmarshal(jsonBytes, &data)
	return data
}
