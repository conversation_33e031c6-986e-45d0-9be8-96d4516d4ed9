package authc

import (
	"log"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// 单测：绑定角色给用户
func TestBindRoleToUserV2(t *testing.T) {
	Convey("BindRoleToUserV2", t, func() {
		//初始化参数
		err := BindRoleToUserV2("lichangyi_dxm", "redis", &[]int{1333})
		So(err, ShouldBeNil)
	})
}

// 单测：删除用户的指定角色
func TestDeleteRoleFromUser(t *testing.T) {
	Convey("DeleteRoleFromUser", t, func() {
		//初始化参数
		err := DeleteRoleFromUser("lichangyi_dxm", "redis", &[]int{1333})
		So(err, ShouldBeNil)
	})
}

// 单测：获取角色所有的绑定用户
func TestGetUsersByRole(t *testing.T) {
	Convey("GetUsersByRole", t, func() {
		//初始化参数
		users, err := GetUsersByRole("redis-manager", "redis")
		log.Println(users)
		So(err, ShouldBeNil)
	})
}

// 单测：获取角色所有的绑定用户
func TestGetRoleByUserID(t *testing.T) {
	Convey("GetRoleByUserID", t, func() {
		//初始化参数
		users, err := GetRoleByUserID(1019, "redis")
		log.Println(users)
		So(err, ShouldBeNil)
	})
}

