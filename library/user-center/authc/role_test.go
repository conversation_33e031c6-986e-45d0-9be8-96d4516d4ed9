package authc

import (
	"log"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// 单测：创建角色
func TestCreateRole(t *testing.T) {
	Convey("CreateRole", t, func() {
		//初始化参数
		users, err := CreateRoleV2(&Role{
			Name:  "2__manager",
			Type:  "redis",
			Alias: "2__manager",
			Params: map[string]string{
				"owner": "jiayiming_dxm",
			},
		})
		log.Println(users)
		So(err, ShouldBeNil)
	})
}

// 单测：删除角色
func TestDeleteRole(t *testing.T) {
	<PERSON>vey("DeleteRole", t, func() {
		//初始化参数
		err := DeleteRoleV2(1400)
		So(err, ShouldBeNil)
	})
}

// 单测：根据name获取role详情
func TestGetRoleByName(t *testing.T) {
	Convey("GetRoleByName", t, func() {
		//初始化参数
		role, err := GetRoleByName("1__manager", "redis")
		log.Println(role)
		So(err, ShouldBeNil)
	})
}
