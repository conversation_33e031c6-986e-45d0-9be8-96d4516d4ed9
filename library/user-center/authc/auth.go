package authc

import (
	"encoding/json"
	"fmt"
	"strconv"
)

type User struct {
	ID   int
	Name string
}

// 验证token
func ValidateToken(token string) (*User, error) {
	subUrl := "/token/tokenValidate"
	body := map[string]any{
		"token": token,
	}

	var res response
	err := post(subUrl, body, &res)
	if err != nil {
		return nil, fmt.Erro<PERSON>("token: %s is not validate, res is: %+v", token, err)
	}

	type metadata struct {
		ID     string `json:"userId"`
		Name   string `json:"username"`
		Tenant string `json:"tenant"`
		IsOK   bool   `json:"isOk"`
	}

	var user metadata
	err = json.Unmarshal(res.Data, &user)
	if err != nil {
		return nil, err
	}

	if !user.IsOK {
		return nil, fmt.Errorf("auth validate not ok")
	}

	userID, err := strconv.Atoi(user.ID)
	if err != nil {
		return nil, fmt.E<PERSON>rf("user id can't convert to int")
	}

	return &User{ID: userID, Name: user.Name}, nil
}
