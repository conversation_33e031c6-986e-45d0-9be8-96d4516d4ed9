package authc

import (
	"log"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// 单测：创建报警策略
func TestValidateToken(t *testing.T) {
	Convey("ValidateToken", t, func() {
		//初始化参数
		user, err := ValidateToken("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiJoYW5zaGVuZ3poYW9fZHhtIiwibmFtZSI6Im5hbWUiLCJ1c2VyX2lkIjoiMSIsInRlbmFudCI6IiIsImV4cCI6MTY5NTIwMjE5OSwiaXNzIjoiZHV4aWFvbWFuIiwibmJmIjoxNjk0NTk3Mzk5fQ.7OAsi7mtjvJTujzQCHSwMRgLL-8-29ZR2WTk1TmODH4")
		log.Println(user)
		So(err, ShouldBeNil)
	})
}
