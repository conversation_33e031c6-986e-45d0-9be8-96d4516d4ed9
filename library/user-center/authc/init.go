package authc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

type Config struct {
	Server    string `yaml:"server"`
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
}

var (
	host string = "http://*************:8322/auth-center/api"
	ak   string = "siod-sc-0ZeI4iDTF2mnJ5L9"
	sk   string = "S3g2LoXPOqjsyWzcGyjFsYAw5ngEf5iM"
)

// 初始化权限中心地址
func Init(cfg *Config) error {
	cfg.Server, _ = strings.CutSuffix(cfg.Server, "/")
	if cfg.Server == "" {
		return fmt.Errorf("config 'auth.server' missing")
	}

	if !strings.HasPrefix(cfg.Server, "http") {
		host = "http://" + cfg.Server + "/api"
	} else {
		host = cfg.Server + "/api"
	}

	if cfg.AccessKey == "" {
		return fmt.Errorf("config 'auth.access_key' missing")
	}
	if cfg.SecretKey == "" {
		return fmt.Errorf("config 'auth.secret_key' missing")
	}

	ak = cfg.AccessKey
	sk = cfg.SecretKey

	return nil
}

// response 子系统下的app查询结果的结构体
type response struct {
	ErrNo  int    `json:"errno"`
	ErrMsg string `json:"errmsg"`
	// 改为使用接口接收数据，因为返回结果可能是字符串
	Data json.RawMessage `json:"data"`
}

// 适用于V1版本的接口请求
func post(subUrl string, reqBody any, v ...any) error {
	if subUrl == "" {
		return fmt.Errorf("subURL is empty")
	}

	url := host + "/v1" + subUrl
	params, _ := json.Marshal(reqBody)
	reader := bytes.NewReader(params)

	client := http.Client{
		Timeout: 5 * time.Second,
	}
	resp, err := client.Post(url, "application/json", reader)
	if err != nil {
		return err
	}

	// 不需要返回值
	if v == nil {
		return nil
	}

	resBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	switch t := v[0].(type) {
	case response:
		err = json.Unmarshal(resBody, &t)
		if err != nil {
			return err
		}
		if t.ErrNo != 0 {
			return fmt.Errorf(t.ErrMsg)
		}
	default:
		err = json.Unmarshal(resBody, &t)
		if err != nil {
			return err
		}
	}

	return nil
}

// 适用于V2版本的接口请求
func encryptPost(subUrl string, reqBody map[string]any) (*response, error) {
	if subUrl == "" {
		return nil, fmt.Errorf("subURL is empty")
	}

	url := host + "/v2" + subUrl

	reqBody["appKey"] = ak
	reqBody["timestamp"] = time.Now().Unix()
	reqBody["sign"] = Sha256Sign(SortValueByKey(reqBody) + sk)

	params, _ := json.Marshal(reqBody)
	reader := bytes.NewReader(params)

	client := http.Client{
		Timeout: 5 * time.Second,
	}
	resp, err := client.Post(url, "application/x-www-form-urlencoded", reader)
	if err != nil {
		return nil, err
	}

	resBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	result := response{}
	err = json.Unmarshal(resBody, &result)
	if err != nil {
		return nil, err
	}
	if result.ErrNo != 0 {
		return nil, fmt.Errorf(result.ErrMsg)
	}

	return &result, nil
}
