package authc

type Department struct {
	ID   int    `json:"departmentID"`
	Name string `json:"departmentName"`
}

// 获取用户所属部门，接口返回值特殊，不符合统一 response 结构
func GetUserDepartment(username string) (*Department, error) {
	subUrl := "/auth/GetUserDepart"
	body := map[string]any{
		"name": username,
	}

	var department Department
	err := post(subUrl, body, &department)
	if err != nil {
		return nil, err
	}

	return &department, nil
}
