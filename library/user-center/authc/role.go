package authc

import (
	"encoding/json"
	"fmt"
	"strconv"
)

type Role struct {
	ID      int               `json:"Id"`
	Name    string            `json:"Name"`
	<PERSON>as   string            `json:"Alias"`
	Type    string            `json:"Type"`
	Comment string            `json:"Comment"`
	Params  map[string]string `json:"params"`
}

// 创建新角色
func CreateRoleV2(role *Role) (id int, err error) {
	subUrl := "/auth/AddRole"

	input := StructToMap(*role)
	res, err := encryptPost(subUrl, input)
	if err != nil {
		return -1, err
	}

	err = json.Unmarshal(res.Data, &id)
	if err != nil {
		return -1, err
	}

	return
}

// 删除角色
func DeleteRoleV2(id int) error {
	subUrl := "/auth/DeleteRole"
	body := map[string]any{
		"roleId": id,
	}

	_, err := encryptPost(subUrl, body)
	if err != nil {
		return err
	}

	return nil
}

// 通过name获取role详情
func GetRoleByName(name string, authType string) (*Role, error) {
	subUrl := "/auth/GetRoleByCondition"
	body := map[string]any{
		"Name": name,
		"Type": authType,
	}

	var rows []map[string]string
	err := post(subUrl, body, &rows)
	if err != nil {
		return nil, err
	}

	if len(rows) == 0 {
		return nil, fmt.Errorf("no role matched")
	}
	if len(rows) > 1 {
		return nil, fmt.Errorf("more then 1 role matched, rows: %+v", rows)
	}

	var role Role
	params := make(map[string]string)
	for k := range rows[0] {
		switch k {
		case "Id":
			role.ID, err = strconv.Atoi(rows[0]["Id"])
			if err != nil {
				return nil, err
			}
		case "Name":
			role.Name = rows[0]["Name"]
		case "Alias":
			role.Alias = rows[0]["Alias"]
		case "Type":
			role.Type = rows[0]["Type"]
		case "Comment":
			role.Comment = rows[0]["Comment"]
		default:
			params[k] = rows[0][k]
		}
	}
	role.Params = params

	return &role, nil
}
