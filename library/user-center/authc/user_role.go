package authc

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
)

// 给用户绑定角色
func BindRoleToUserV2(username string, authType string, roleIDs *[]int) error {
	subUrl := "/auth/BindRoleToUser"
	body := map[string]any{
		"name":      username,
		"auth_type": authType,
		"role_ids":  roleIDs,
	}

	_, err := encryptPost(subUrl, body)
	if err != nil {
		return fmt.Errorf("bind user error with params: %+v, err: %s", body, err.Error())
	}

	return nil
}

// 删除用户的指定角色
func DeleteRoleFromUser(username string, authType string, roleIDs *[]int) error {
	subUrl := "/auth/DeleteUserRole"
	body := map[string]any{
		"userName": username,
		"authType": authType,
		"roleIds":  roleIDs,
	}

	_, err := encryptPost(subUrl, body)
	if err != nil {
		return fmt.Errorf("delete user role with params: %+v, err: %s", body, err.Error())
	}

	return nil
}

// 根据角色获取用户
func GetUsersByRole(roleName string, roleType string) ([]string, error) {
	subUrl := "/auth/GetUsersByRole"
	body := map[string]any{
		"name": roleName,
		"type": roleType,
	}

	var res response
	err := post(subUrl, body, &res)
	if err != nil {
		return nil, err
	}

	var userStr string
	err = json.Unmarshal(res.Data, &userStr)
	if err != nil {
		return nil, err
	}

	return strings.Split(userStr, ","), nil
}

// ============================
//  	获取用户的所有角色
// ============================

// 根据UserID获取角色，AuthType不同返回的角色列表可能不同
func GetRoleByUserID(userID int, authType string) (*[]Role, error) {
	subUrl := "/auth/GetRoleByUserID"
	body := map[string]any{
		"userID":   userID,
		"authType": authType,
	}

	var rows []map[string]string
	err := post(subUrl, body, &rows)
	if err != nil {
		return nil, fmt.Errorf("get user role error with id:%v, authType:%s, err:%s", userID, authType, err.Error())
	}

	roles := make([]Role, len(rows))
	for i, row := range rows {
		params := make(map[string]string)
		for k := range row {
			switch k {
			case "Id":
				roles[i].ID, err = strconv.Atoi(row["Id"])
				if err != nil {
					return nil, err
				}
			case "Name":
				roles[i].Name = row["Name"]
			case "Alias":
				roles[i].Alias = row["Alias"]
			case "Type":
				roles[i].Type = row["Type"]
			case "Comment":
				roles[i].Comment = row["Comment"]
			default:
				params[k] = row[k]
			}
		}
		roles[i].Params = params
	}

	return &roles, nil
}
