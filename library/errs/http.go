package errs

import (
	"net/http"
	"strings"
)

type HttpError struct {
	Code    Code
	Status  int
	Message string
	IV      int // 前端只认int，转换一下
}

// 实现error接口的Error()方法
func (err HttpError) Error() string {
	return err.Message
}

// 初始化一个HttpError
func New(code Code, msg ...string) *HttpError {
	err := HttpError{
		Code:   code,
		Status: http.StatusBadRequest,
	}

	if v, exist := eMap[code]; exist {
		err.Status = v.Status
		err.Message = v.Message
	}

	totalMessage := strings.Join(msg, ", ")
	if totalMessage != "" {
		err.Message = totalMessage
	}

	return &err
}
