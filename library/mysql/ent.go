package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	entSql "entgo.io/ent/dialect/sql"
	_ "github.com/go-sql-driver/mysql"

	"sla-service/library/ent"
)

const (
	ModeRelease = "release"
	ModeMock    = "mock"
)

var (
	clients map[string]*Client
	timeout time.Duration
	mode    string = ModeRelease
)

type Config struct {
	Host              string        `yaml:"host"`
	Port              string        `yaml:"port"`
	Name              string        `yaml:"name"`
	Alias             string        `yaml:"alias"`
	Username          string        `yaml:"user"`
	Password          string        `yaml:"password"`
	Mode              bool          `yaml:"mode"`
	MaxIdleConns      int           `yaml:"max_idle_conns"`
	MaxOpenConns      int           `yaml:"max_open_conns"`
	MaxLifetime       time.Duration `yaml:"max_lifetime"`        // 连接存活时长，单位s，默认 300s
	SingeQueryTimeout time.Duration `yaml:"singe_query_timeout"` // 查询的超时时长，单位s，默认30s
	AutoMigrate       bool
}

// 初始化数据库连接
func Init(cfg *Config) (*ent.Client, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config required but missing")
	}

	if cfg.Alias == "" {
		cfg.Alias = "default"
	}
	if cfg.Host == "" {
		return nil, fmt.Errorf("params host required but missing")
	}
	if cfg.Port == "" {
		return nil, fmt.Errorf("params port required but missing")
	}
	if cfg.Name == "" {
		return nil, fmt.Errorf("params name required but missing")
	}
	if cfg.Username == "" {
		return nil, fmt.Errorf("params username required but missing")
	}
	if cfg.Password == "" {
		return nil, fmt.Errorf("params password required but missing")
	}
	if cfg.MaxIdleConns == 0 {
		cfg.MaxIdleConns = 5
	}
	if cfg.MaxOpenConns == 0 {
		cfg.MaxOpenConns = 10
	}
	if cfg.MaxLifetime == 0 {
		cfg.MaxLifetime = 300
	}
	if cfg.SingeQueryTimeout == 0 {
		cfg.SingeQueryTimeout = 30
	}

	auth := cfg.Username + ":" + cfg.Password + "@(" + cfg.Host + ":" + cfg.Port + ")/" + cfg.Name + "?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := sql.Open("mysql", auth)
	if err != nil {
		return nil, fmt.Errorf("connect error, %v", err.Error())
	}

	// config set
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetConnMaxLifetime(cfg.MaxLifetime * time.Second)

	// ent clinet
	drv := entSql.OpenDB("mysql", db)

	client := ent.NewClient(ent.Driver(drv))
	log.Println("mysql connect successfully")

	timeout = cfg.SingeQueryTimeout * time.Second
	if cfg.AutoMigrate {
		ctx := context.Background()
		err := client.Schema.Create(ctx)
		if err != nil {
			log.Fatalf("failed creating schema resources: %v", err)
		}
	}

	// save client
	clients = make(map[string]*Client)
	if _, exists := clients[cfg.Alias]; exists {
		return nil, fmt.Errorf("database %s already exists", cfg.Alias)
	}
	clients[cfg.Alias] = &Client{client}
	mode = ModeRelease

	return client, nil
}

// 返回全局ent client对象
func Database(name ...string) (*Client, error) {
	dbName := "default"
	if len(name) != 0 {
		dbName = name[0]
	}

	client, exists := clients[dbName]
	if !exists {
		return nil, fmt.Errorf("can't find db client, name as %s", dbName)
	}

	return client, nil
}

// 返回带有超时时间的context，超时时间不指定使用配置文件中的timeout，默认10s
func ContextWithTimeout(seconds ...time.Duration) (context.Context, context.CancelFunc) {
	t := timeout
	if len(seconds) != 0 {
		t = seconds[0] * time.Second
	}

	return context.WithTimeout(context.Background(), t)
}
