package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// SLI holds the schema definition for the SLI entity.
type SLI struct {
	ent.Schema
}

// Table returns the table name for the SLI entity.
func (SLI) Table() string {
	return "sli"
}

// Fields of the SLI.
func (SLI) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		// 所属SLA
		field.Int64("sla_id").StructTag(`json:"slaId"`).Optional().Default(0).Comment("所属SLA ID"),
		field.String("sla_name").StructTag(`json:"slaName"`).NotEmpty().Default("").Comment("所属SLA名称"),

		// SLI信息
		field.String("name").NotEmpty().Default("").Comment("SLI名称"),
		field.String("alias").NotEmpty().Default("").Comment("SLI别名"),
		field.String("description").Optional().Default("").Comment("SLI描述"),

		// 指标配置
		field.String("unit").NotEmpty().Default("").Comment("单位，同时表示值类型：percentage/ms/rps等"),
		field.String("numerator_name").StructTag(`json:"numeratorName"`).NotEmpty().Default("").Comment("分子指标名称（如downtime_minutes、pv_lost）"),
		field.String("denominator_name").StructTag(`json:"denominatorName"`).Optional().Default("").Comment("分母指标名称（如pv_total，当分母为固定时间时可为空）"),
		field.String("denominator_source").StructTag(`json:"denominatorSource"`).NotEmpty().Default("event").Comment("分母数据来源：event/time_minutes/time_5minutes"),
		field.String("comparison").NotEmpty().Default("").Comment("比较方式：gt/gte/lt/lte"),
		field.Float("threshold").Default(0).Comment("阈值"),
		field.Int("decimal_places").StructTag(`json:"decimalPlaces"`).Default(2).Comment("数据展示时保留的小数位数"),

		// 状态信息
		field.String("status").NotEmpty().Default("normal").Comment("状态: normal/deleted"),
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).UpdateDefault(time.Now).Default(time.Now).Comment("最近一次更新时间"),
	}
}

// Edges of the SLI.
func (SLI) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("sla", SLA.Type).Ref("slis").Unique().Field("sla_id").Comment("所属SLA"),

		edge.To("events", Event.Type).Comment("SLI相关事件"),
		edge.To("calculations", Calculation.Type).Comment("SLA计算结果"),
	}
}

// Indexes of the SLI.
func (SLI) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("sla_name", "name").Unique(),
	}
}

// Annotations of the SLI.
func (SLI) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "sli"},
	}
}
