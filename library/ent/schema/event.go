package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Event holds the schema definition for the Event entity.
type Event struct {
	ent.Schema
}

// Table returns the table name for the Event entity.
func (Event) Table() string {
	return "event"
}

// Fields of the Event.
func (Event) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		// 关联信息
		field.Int64("sla_id").StructTag(`json:"slaId"`).Default(0).Comment("关联SLA ID"),
		field.String("sla_name").Optional().Default("").Comment("关联SLA名称"),
		field.Int64("sli_id").StructTag(`json:"sliId"`).Default(0).Comment("关联SLI ID"),
		field.String("sli_name").Optional().Default("").Comment("关联SLI名称"),
		field.Int64("service_id").StructTag(`json:"serviceId"`).Default(0).Comment("关联服务ID"),
		field.String("service_name").Optional().Default("").Comment("关联服务名称"),

		// 事件信息
		field.String("event_type").StructTag(`json:"eventType"`).NotEmpty().Default("").Comment("事件类型：metric_report/incident/maintenance等"),
		field.Float("numerator").Optional().Default(0).Comment("分子值"),
		field.Float("denominator").Optional().Default(0).Comment("分母值（当分母为固定时间时可为空）"),
		field.String("description").Optional().Default("").Comment("事件描述"),
		field.String("reporter").Optional().Default("").Comment("上报人"),

		// 时间信息
		field.Time("happened_at").StructTag(`json:"happenedAt"`).Default(time.Now).Comment("时间发生时间"),
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
	}
}

// Edges of the Event.
func (Event) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("service", Service.Type).Ref("events").Unique().Required().Field("service_id").Comment("关联服务"),
		edge.From("sli", SLI.Type).Ref("events").Unique().Required().Field("sli_id").Comment("关联SLI"),
	}
}

// Indexes of the Event.
func (Event) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("sla_id"),
		index.Fields("sli_id", "service_id", "event_type", "happened_at").Unique(),
	}
}

// Annotations of the Event.
func (Event) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "event"},
	}
}
