package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// SLA holds the schema definition for the SLA entity.
type SLA struct {
	ent.Schema
}

// Table returns the table name for the SLA entity.
func (SLA) Table() string {
	return "sla"
}

// Fields of the SLA.
func (SLA) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		// 所属产品
		field.Int64("product_id").StructTag(`json:"productId"`).Optional().Default(0).Comment("所属产品ID"),
		field.String("product_name").StructTag(`json:"productName"`).NotEmpty().Default("").Comment("所属产品名称"),

		// SLA套餐信息
		field.String("name").NotEmpty().Default("").Comment("名称"),
		field.String("alias").NotEmpty().Default("").Comment("别名"),
		field.String("description").Optional().Default("").Comment("描述"),

		// SLA周期配置
		field.String("period").NotEmpty().Default("monthly").Comment("SLA周期：monthly/quarterly/yearly"),

		// 状态信息
		field.String("status").NotEmpty().Default("normal").Comment("状态: normal/deleted"),
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).UpdateDefault(time.Now).Default(time.Now).Comment("最近一次更新时间"),
	}
}

// Edges of the SLA.
func (SLA) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("product", Product.Type).Ref("slas").Field("product_id").Unique().Comment("所属产品"),

		edge.To("slis", SLI.Type).Comment("SLA包含的SLI"),
		edge.To("services", Service.Type).Comment("使用此SLA的服务"),
	}
}

// Indexes of the SLA.
func (SLA) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("product_name", "name").Unique(),
	}
}

// Annotations of the SLA.
func (SLA) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "sla"},
	}
}
