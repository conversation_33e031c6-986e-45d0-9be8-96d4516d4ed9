package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Product holds the schema definition for the Product entity.
type Product struct {
	ent.Schema
}

// Table returns the table name for the Product entity.
func (Product) Table() string {
	return "product"
}

// Fields of the Product.
func (Product) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		field.String("name").Unique().NotEmpty().Comment("产品名称"),
		field.String("alias").NotEmpty().Default("").Comment("产品别名"),
		field.String("description").Optional().Comment("产品描述"),

		// 业务信息
		field.String("department").Optional().Default("").Comment("所属部门"),
		field.String("owner").Optional().Default("").Comment("产品负责人"),

		// 状态信息
		field.String("status").NotEmpty().Default("normal").Comment("状态: normal/deleted"),
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).UpdateDefault(time.Now).Default(time.Now).Comment("最近一次更新时间"),
	}
}

// Edges of the Product.
func (Product) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("slas", SLA.Type).Comment("产品的SLA套餐"),
		edge.To("services", Service.Type).Comment("产品的服务"),
	}
}

// Indexes of the Product.
func (Product) Indexes() []ent.Index {
	return []ent.Index{}
}

// Annotations of the Product.
func (Product) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "product"},
	}
}
