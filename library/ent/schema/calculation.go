package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Calculation holds the schema definition for the Calculation entity.
type Calculation struct {
	ent.Schema
}

// Table returns the table name for the Calculation entity.
func (Calculation) Table() string {
	return "calculation"
}

// Fields of the Calculation.
func (Calculation) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),
		field.Int64("service_id").StructTag(`json:"serviceId"`).Default(0).Comment("服务ID"),
		field.String("service_name").StructTag(`json:"serviceName"`).NotEmpty().Default("").Comment("服务名称"),
		field.Int64("sli_id").StructTag(`json:"sliId"`).Default(0).Comment("SLI ID"),
		field.String("sli_name").StructTag(`json:"sliName"`).NotEmpty().Default("").Comment("SLI名称"),

		// 计算周期
		field.Time("start_time").StructTag(`json:"startTime"`).Comment("本次计算的时间范围开始"),
		field.Time("end_time").StructTag(`json:"endTime"`).Comment("本次计算的时间范围结束"),

		// 分子分母数据
		field.Float("numerator").Default(0).Comment("分子累加值"),
		field.Float("denominator").Default(0).Comment("分母值（累加值或固定时间值）"),

		// SLI计算结果
		field.Float("value").Default(0).Comment("SLI计算值（百分比）"),
		field.Bool("breached").Default(false).Comment("SLI是否被打破"),

		// 数据统计
		field.Int("event_count").StructTag(`json:"eventCount"`).Default(0).Comment("参与计算的事件数量"),

		// 状态信息
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).UpdateDefault(time.Now).Default(time.Now).Comment("最近一次更新时间"),
	}
}

// Edges of the Calculation.
func (Calculation) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("service", Service.Type).Ref("calculations").Unique().Required().Field("service_id").Comment("关联服务"),
		edge.From("sli", SLI.Type).Ref("calculations").Unique().Required().Field("sli_id").Comment("关联SLI"),
	}
}

// Indexes of the Calculation.
func (Calculation) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("service_name"),
		index.Fields("service_name", "sli_name", "end_time").Unique(),
	}
}

// Annotations of the Calculation.
func (Calculation) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "calculation"},
	}
}
