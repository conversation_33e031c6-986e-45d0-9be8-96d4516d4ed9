package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Service holds the schema definition for the Service entity.
type Service struct {
	ent.Schema
}

// Table returns the table name for the Service entity.
func (Service) Table() string {
	return "service"
}

// Fields of the Service.
func (Service) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),
		
		// 所属产品和SLA
		field.Int64("product_id").StructTag(`json:"productId"`).Optional().Default(0).Comment("所属产品ID"),
		field.String("product_name").StructTag(`json:"productName"`).NotEmpty().Default("").Comment("所属产品名称"),
		field.Int64("sla_id").StructTag(`json:"slaId"`).Optional().Default(0).Comment("绑定的SLA套餐ID"),
		field.String("sla_name").StructTag(`json:"slaName"`).NotEmpty().Default("").Comment("绑定的SLA套餐名称"),

		// 服务信息
		field.String("name").NotEmpty().Default("").Comment("服务名称"),
		field.String("alias").NotEmpty().Default("").Comment("别名"),
		field.String("description").Optional().Default("").Comment("服务描述"),

		// 状态信息
		field.String("status").NotEmpty().Default("normal").Comment("状态: normal/deleted"),
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).UpdateDefault(time.Now).Default(time.Now).Comment("最近一次更新时间"),
	}
}

// Edges of the Service.
func (Service) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("product", Product.Type).Ref("services").Unique().Field("product_id").Comment("所属产品"),
		edge.From("sla", SLA.Type).Ref("services").Unique().Field("sla_id").Comment("绑定的SLA套餐"),

		edge.To("events", Event.Type).Comment("服务相关事件"),
		edge.To("calculations", Calculation.Type).Comment("SLA计算结果"),
	}
}

// Indexes of the Service.
func (Service) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("sla_name"),
		index.Fields("product_name", "name").Unique(),
	}
}

// Annotations of the Service.
func (Service) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "service"},
	}
}
