package logger

import "go.uber.org/zap"

type C struct {
	zap *zap.Logger
}

func (c *C) Debug(format string, v ...any) {
	c.zap.Sugar().Debugf(format, v...)
}

func (c *C) Info(format string, v ...any) {
	c.zap.Sugar().Infof(format, v...)
}

func (c *C) Warn(format string, v ...any) {
	c.zap.Sugar().Warnf(format, v...)
}

func (c *C) Error(format string, v ...any) {
	c.zap.Sugar().Errorf(format, v...)
}

func Client() *C {
	return &C{zap: logger}
}
