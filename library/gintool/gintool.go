package gintool

import (
	"fmt"
	"math/rand"
	"net"
	"net/http/httputil"
	"os"
	"runtime/debug"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"sla-service/library/errs"
	"sla-service/library/logger"
)

// 为每个请求加上ReqID
func Identity() gin.HandlerFunc {
	var letterRunes = []rune("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
	return func(c *gin.Context) {
		reqID := c.GetHeader("ReqID")
		if reqID == "" {
			now := time.Now()
			b := make([]rune, 12)
			for i := range b {
				b[i] = letterRunes[rand.Intn(len(letterRunes))]
			}
			reqID = now.Format("200601_0215") + "_" + string(b)
		}
		c.Set("ReqID", reqID)
		c.Next()
	}
}

// Gin Logger 用于替换gin框架的Logger中间件
func Logger(c *gin.Context) {
	start := time.Now()
	path := c.Request.URL.Path
	query := c.Request.URL.RawQuery
	c.Next()
	// 视图函数执行完成，统计时间，记录日志
	cost := time.Since(start)
	if query != "" {
		path = fmt.Sprintf("%v?%v", path, query)
	}
	logger.Info("[%v] %v:%v", cost, c.Request.Method, path)
}

// Gin Recovery 用于替换gin框架的Recovery中间件
func Recovery(c *gin.Context) {
	defer func() {
		// defer 延迟调用，出了异常，处理并恢复异常，记录日志
		if err := recover(); err != nil {
			// 这个不必须，检查是否存在断开的连接(broken pipe或者connection reset by peer)---------开始--------
			var brokenPipe bool
			if ne, ok := err.(*net.OpError); ok {
				if se, ok := ne.Err.(*os.SyscallError); ok {
					if strings.Contains(strings.ToLower(se.Error()), "broken pipe") || strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
						brokenPipe = true
					}
				}
			}
			//httputil包预先准备好的DumpRequest方法
			httpRequest, _ := httputil.DumpRequest(c.Request, false)
			if brokenPipe {
				logger.Warn(c.Request.URL.Path,
					zap.Any("error", err),
					zap.String("request", string(httpRequest)),
				)
				// 如果连接已断开，我们无法向其写入状态
				c.Error(err.(error))
				c.Abort()
				return
			}

			// 打印堆栈信息
			logger.Error("[Panic Occured] %v", err)
			logger.Error("[Recovery From Panic] %v", string(debug.Stack()))
			JSON(c, nil, errs.CodeUnknown.Detail())
		}
	}()
	c.Next()
}
