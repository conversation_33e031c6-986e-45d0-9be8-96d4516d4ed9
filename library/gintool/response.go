package gintool

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"sla-service/library/errs"
)

// 分页结构
type Paging struct {
	Count int         `json:"count"`
	Rows  any         `json:"rows"`
	Page  int         `json:"page"`
	Size  int         `json:"size"`
}

func FormatPaging(count int, rows any, page int, pageSize int) *Paging {
	return &Paging{
		Count: count,
		Rows:  rows,
		Page:  page,
		Size:  pageSize,
	}
}

// Response 标准响应结构
type Response struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data,omitempty"`
}

func JSON(c *gin.Context, data any, err error) {
	var b Response
	// b.ReqID = c.MustGet("ReqID").(string)

	if err != nil {
		switch e := err.(type) {
		case *errs.HttpError:
			b.Code = string(e.Code)
			b.Msg = e.Error()
			c.Writer.WriteHeader(e.Status)
		case errs.Code:
			b.Code = string(e)
			b.Msg = e.Error()
			c.Writer.WriteHeader(e.Status())
		default:
			b.Code = string(errs.CodeUnknown)
			b.Msg = e.Error()
			c.Writer.WriteHeader(http.StatusInternalServerError)
		}
	} else if data == nil {
		b.Code = string(errs.CodeVoidReturn)
		b.Msg = "no return value"
		c.Writer.WriteHeader(http.StatusInternalServerError)
	} else {
		b.Code = string(errs.Success)
		b.Data = data
	}

	c.JSON(http.StatusOK, b)
	c.Abort()
}

// 适用于与前端对接
type body2fe struct {
	Code    int    `json:"code"`
	Message string `json:"errMsg,omitempty"`
	Data    any    `json:"data,omitempty"`
}

// 适用于与前端对接的返回值格式化方法
func JSON2FE(c *gin.Context, data any, err error) {
	var b body2fe
	// b.ReqID = c.MustGet("ReqID").(string)

	if err != nil {
		switch e := err.(type) {
		case *errs.HttpError:
			b.Code = e.Code.Int()
			b.Message = e.Error()
			c.Writer.WriteHeader(e.Status)
		case errs.Code:
			b.Code = e.Int()
			b.Message = e.Error()
			c.Writer.WriteHeader(e.Status())
		default:
			b.Code = 1
			b.Message = e.Error()
			c.Writer.WriteHeader(http.StatusInternalServerError)
		}
	} else if data == nil {
		b.Code = errs.CodeVoidReturn.Int()
		b.Message = "no return value"
		c.Writer.WriteHeader(http.StatusInternalServerError)
	} else {
		b.Code = 0
		b.Data = data
	}

	c.JSON(http.StatusOK, b)
	c.Abort()
}
