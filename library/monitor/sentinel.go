package monitor

import (
	"fmt"
	"strconv"

	"dt-common/noah"
)

// Sentinel模块监控管理
type Sentinel struct {
	*App
	ClusterName string
}

// 监控任务管理
func (o *Sentinel) Monitors(opt string) error {
	o.logCollectRexpMap = map[string]*map[string]string{
		"sentinel": {
			"switch":              "(switch)",
			"odown":               "(\\+odown)",
			"TILT_ON":             "(tilt mode entered)",
			"TILT_OFF":            "(tilt mode exited)",
			"sdown":               "(\\+sdown)",
			"whitelist_proc_exit": "(Bns whitelist exited unexpectly)",
			"failover_delay":      "(failover delay)",
		},
		"whitelist": {
			"ip_forbidden":    "(client's ip is not in the whitelist)",
			"bns_not_exist":   "(won't enter PROTECT MODE)",
			"protect_flag_ON": "(protect_flag is ON)",
			"bns_error":       "(The BNS name cannot be analyzed)",
		},
	}

	tasks := [][]string{
		{"process", "sentinel", "/home/<USER>/local/sentinel/bin/redis-sentinel", "进程监控", "10"},
		{"process", "agent", "/home/<USER>/redis-agent/cmd/redis-agent", "redis-agent进程监控", "10"},

		{"port", fmt.Sprintf("sentinel_%d", o.Port), strconv.Itoa(o.Port), "端口监控", "10"},
		{"port", fmt.Sprintf("agent_%d", agentPort), strconv.Itoa(agentPort), "redis-agent端口监控", "10"},

		{"logCollect", "sentinel", "/home/<USER>/local/sentinel/log/sentinel.log", "进程日志采集", "10", "sentinel"},
		{"logCollect", "whitelist", "/home/<USER>/local/sentinel/log/whitelist.log", "白名单日志采集", "60", "whitelist"},

		{"exec", "core", "/home/<USER>/redis_monitor/out_core.sh", "out core监控", "10"},
	}

	switch opt {
	case "add":
		return o.addMonitors(&tasks)
	case "del":
		return o.delMonitors(&tasks)
	default:
		return fmt.Errorf("opt should be one of add or del")
	}
}

// 管理报警策略，add:添加报警策略, del:删除报警策略。
// Critical需要立即处理，马上就死或已经死了，Major需要尽快处理，不处理有可能会死，Warning需要关注风险，短期不处理问题不大，Notice收到即可
func (o *Sentinel) Alarms(opt string) error {
	// 生效范围： deploy: docker
	o.SetDimension("deployDocker", &DimensionParams{
		Type: "deploy",
		Dimensions: &[]noah.PolicyDimension{
			{Name: "deploy", Type: "EQUAL", Values: []string{"docker"}},
		},
	})

	alarms := [][]string{
		// ** 容器监控 **//
		{"HOST", "critical", "machine_down", "machine_down", "STATUS_PING{_statistic='avg'}==0", "3", "3"},
		{"HOST", "critical", "machine_mem_req_100", "machine_mem_req_100", "MEM_USED_PERCENT{_statistic='avg'}>=100", "3", "3"},

		// ** 进程监控 **//
		{"INSTANCE", "critical", "cpu_usage_80", "cpu_usage_80", "process.sentinel.cpu_usage>0.8", "deployDocker"},
		{"INSTANCE", "major", "cpu_usage_60", "cpu_usage_60", "process.sentinel.cpu_usage>0.6", "deployDocker", "30", "30"},

		// ** 端口监控 **//
		{"INSTANCE", "critical", fmt.Sprintf("sentinel_%d_dead", o.Port), fmt.Sprintf("sentinel_%d_dead", o.Port), fmt.Sprintf("port.sentinel_%d.status!='ok'", o.Port), "deployDocker"},
		{"INSTANCE", "critical", fmt.Sprintf("agent_%d_dead", agentPort), fmt.Sprintf("agent_%d_dead", agentPort), fmt.Sprintf("port.agent_%d.status!='ok'", agentPort), "deployDocker"},

		// ** 日志监控 **//
		{"INSTANCE", "critical", "failover_delay", "failover_delay", "log.sentinel.failover_delay{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "major", "bns_error", "bns_error", "log.whitelist.bns_error{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "major", "bns_not_exist", "bns_not_exist", "log.whitelist.bns_not_exist{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "warning", "odown", "odown", "log.sentinel.odown{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "warning", "sdown", "sdown", "log.sentinel.sdown{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "warning", "protect_flag_ON", "protect_flag_ON", "log.whitelist.protect_flag_ON{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "warning", "whitelist_proc_exit", "whitelist_proc_exit", "log.sentinel.whitelist_proc_exit{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "warning", "TILT_ON", "TILT_ON", "log.sentinel.TILT_ON{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "warning", "TILT_OFF", "TILT_OFF", "log.sentinel.TILT_OFF{_statistic='count'}>0", "deployDocker"},
		{"INSTANCE", "notice", "switch", "switch", "log.sentinel.switch{_statistic='count'}>0", "deployDocker"},

		// ** 自定义监控 **//
		{"INSTANCE", "critical", "out_core", "out_core", "exec.core.core_num{_statistic='avg'}==1", "deployDocker"},
	}
	switch opt {
	case "add":
		return o.addAlarms(&alarms)
	case "del":
		return o.delAlarms(&alarms)
	default:
		return fmt.Errorf("opt should be one of add or del")
	}
}

// 清理物理实例的监控采集和报警策略
func (o *Sentinel) Clean(section string) error {
	portStr := strconv.Itoa(o.Port)
	switch section {
	case "monitor":
		tasks := [][]string{
			{"process", "sentinel_" + portStr, "/home/<USER>/local/sentinel_" + portStr + "/bin/redis-sentinel", "进程监控", "10"},
			{"port", "port_" + portStr, portStr, "端口监控", "10"},
			{"logCollect", "sentinel_" + portStr, "/home/<USER>/local/sentinel_" + portStr + "/log/sentinel.log", "进程日志采集", "10", "sentinel"},
			{"logCollect", "sentinel_whitelist_" + portStr, "/home/<USER>/local/sentinel_" + portStr + "/log/whitelist.log", "白名单日志采集", "60", "whitelist"},
		}
		err := o.delMonitors(&tasks)
		if err != nil {
			return err
		}
	case "alarm":
		alarms := [][]string{
			{"INSTANCE", "critical", "sentinel_port_" + portStr + "_dead"},
			{"INSTANCE", "critical", "sentinel_process_" + portStr + "_dead"},
			{"INSTANCE", "critical", "sentinel_" + portStr + "_failover_delay"},
			{"INSTANCE", "warning", "sentinel_" + portStr + "_switch"},
			{"INSTANCE", "warning", "sentinel_" + portStr + "_odown"},
			{"INSTANCE", "warning", "sentinel_" + portStr + "_TILT_ON"},
			{"INSTANCE", "warning", "sentinel_" + portStr + "_TILT_OFF"},
			{"INSTANCE", "warning", "sentinel_" + portStr + "_sdown"},
			{"INSTANCE", "major", "sentinel_process_" + portStr + "_cpu_usage_exceed_35"},
			{"INSTANCE", "major", "sentinel_process_" + portStr + "_cpu_usage_exceed_50"},
			{"INSTANCE", "major", "sentinel_bns_error_" + portStr + ""},
			{"INSTANCE", "warning", "sentinel_bns_not_exist_" + portStr + ""},
			{"INSTANCE", "major", "sentinel_protect_flag_ON_" + portStr + ""},
			{"INSTANCE", "major", "sentinel_whitelist_proc_exit_" + portStr + ""},
		}
		err := o.delAlarms(&alarms)
		if err != nil {
			return err
		}
	}

	return nil
}
