package router

import (
	"github.com/gin-gonic/gin"

	"sla-service/controller/event"
	"sla-service/controller/product"
	"sla-service/library/errs"
	"sla-service/library/gintool"
)

// 适用于gin的鉴权中间件，简单鉴权，只验证Token
func AuthFilter(token string) gin.HandlerFunc {
	return func(c *gin.Context) {
		t := c.Request.Header.Get("Authorization")
		if t == "" || t != token {
			gintool.JSON(c, nil, errs.New(errs.CodeAuthenticationFailed))
			return
		}
	}
}

// Routes ras接口
func Routes(r *gin.Engine, token string) {
	// 产品管理路由
	productGroup := r.Group("/api/products")
	{
		productGroup.POST("", product.Create)       // 创建产品
		productGroup.GET("/:id", product.Get)       // 获取单个产品
		productGroup.GET("", product.List)          // 获取产品列表
		productGroup.PUT("/:id", product.Update)    // 更新产品
		productGroup.DELETE("/:id", product.Delete) // 删除产品
	}

	// 事件管理路由
	eventGroup := r.Group("/api/events")
	{
		eventGroup.POST("", event.Report) // 事件上报
	}
}
