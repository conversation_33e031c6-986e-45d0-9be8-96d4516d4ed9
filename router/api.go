package router

import (
	"github.com/gin-gonic/gin"

	"sla-service/controller/event"
	"sla-service/library/errs"
	"sla-service/library/gintool"
)

// 适用于gin的鉴权中间件，简单鉴权，只验证Token
func AuthFilter(token string) gin.HandlerFunc {
	return func(c *gin.Context) {
		t := c.Request.Header.Get("Authorization")
		if t == "" || t != token {
			gintool.JSON(c, nil, errs.New(errs.CodeAuthenticationFailed))
			return
		}
	}
}

// Routes ras接口
func Routes(r *gin.Engine, token string) {
	// 产品管理路由
	apiGroup := r.Group("/api")
	{
		apiGroup.POST("/event/report", event.Report) // 事件上报
		apiGroup.POST("/calculate", event.Calculate) // 触发SLA计算
	}
}
