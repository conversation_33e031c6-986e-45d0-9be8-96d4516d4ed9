package env

import (
	"log"

	"sla-service/config"
	"sla-service/library/logger"
	"sla-service/library/mysql"
)

// Application Config
type Application struct {
	Mode  string `yaml:"mode"`
	Port  int    `yaml:"port"`
	Token string `yaml:"token"`
	BNS   string `yaml:"bns"`
}

func Init(configPath ...string) *Application {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("failed to init config, error=(%v)", err)
	}

	// 获取app config
	var appConfig Application
	err = config.Get("application", &appConfig)
	if err != nil {
		log.Panicf("failed to init application, error=(%v)", err)
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("failed to init logger, error=(%v)", err)
	}
	logger.Init(&logConfig)

	// 初始化 db
	var dbConfig mysql.Config
	err = config.Get("db", &dbConfig)
	dbConfig.AutoMigrate = true
	if err != nil {
		log.Panicf("failed to init mysql, error=(%v)", err)
	}
	mysql.Init(&dbConfig)

	return &appConfig
}
